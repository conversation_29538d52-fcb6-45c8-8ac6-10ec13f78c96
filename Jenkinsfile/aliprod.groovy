@Library('common-shared-library@xsjl') _

def map = [:]
    // 定义项目构建运行的命名空间
    map.put('NAMESPACE','xsjl')
    // 需要修改此处，定义微服务工程名称
    map.put('PROJECT_NAME','xsjlqueue')
    // 定义服务配置
    map.put('PROFILES','aliprod')
    // 定义jvm
    map.put('JVM_OPTIONS','-server -Ddruid.mysql.usePingMethod=false -Dio.netty.leakDetectionLevel=advanced -Djava.security.egd=file:/dev/./urandom -Xms512m -Xmx512m -Xss512K -XX:+HeapDumpOnOutOfMemoryError')
    // 定义端口
    map.put('PORT','12002')
    // 定义项目git地址
    map.put('SrcUrl','http://*********/stu/xsjlqueue.git')
    // 确定镜像名称 [格式: 项目名/服务名]
    map.put('IMAGES_ORG','hlyz')
  
deploy_aliack_cluster(map)

@Library('common-shared-library@xsjl') _

def map = [:]
    // 定义项目构建运行的命名空间
    map.put('NAMESPACE','xsjl-dev')
    // 需要修改此处，定义微服务工程名称
    map.put('PROJECT_NAME','xsjlqueue')
    // 修改jar包名称
    map.put('JAR_NAME','xsjlqueue.jar')
    // 定义服务配置
    map.put('PROFILES','k8sdev')
    // 定义jvm
    map.put('JVM_OPTIONS','-server -Ddruid.mysql.usePingMethod=false -Dio.netty.leakDetectionLevel=advanced -Djava.security.egd=file:/dev/./urandom -Xms512m -Xmx512m -Xss512K -XX:+HeapDumpOnOutOfMemoryError')
    // 定义端口
    map.put('PORT','12002')
    //定义映射端口
    map.put('BIND_PORT','12002')
    // 确定pod副本数
    //map.put('REPLICAS','2')
    // 定义项目git地址
    map.put('SrcUrl','http://10.30.5.8/stu/xsjlqueue.git')
    // 确定镜像组织/项目名称
    map.put('IMAGES_ORG','xsjl-dev')
  
deploy_idc_cluster(map)

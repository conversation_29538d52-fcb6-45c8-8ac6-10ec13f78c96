<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlqueue.dal.dao.XsPointRecordMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlqueue.dal.entity.XsPointRecord">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="student_id" jdbcType="BIGINT" property="studentId"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="account_name" jdbcType="VARCHAR" property="accountName"/>
        <result column="score" jdbcType="DECIMAL" property="score"/>
        <result column="plan_id" jdbcType="INTEGER" property="planId"/>
        <result column="channel" jdbcType="TINYINT" property="channel"/>
        <result column="scene" jdbcType="VARCHAR" property="scene"/>
        <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId"/>
        <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId"/>
        <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="plan_comment_id" jdbcType="VARCHAR" property="planCommentId"/>
        <result column="plan_comment_content" jdbcType="VARCHAR" property="planCommentContent"/>
        <result column="plan_tag_id" jdbcType="BIGINT" property="planTagId"/>
        <result column="plan_tag_name" jdbcType="VARCHAR" property="planTagName"/>
        <result column="game_id" jdbcType="INTEGER" property="gameId"/>
        <result column="game_name" jdbcType="VARCHAR" property="gameName"/>
        <result column="game_record_id" jdbcType="INTEGER" property="gameRecordId"/>
        <result column="game_record_title" jdbcType="VARCHAR" property="gameRecordTitle"/>
        <result column="module_code" jdbcType="INTEGER" property="moduleCode"/>
        <result column="apply_level" jdbcType="VARCHAR" property="applyLevel"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        id, content, student_id, user_id, account_name, score, plan_id, channel, scene, saas_class_id,
        saas_school_id, saas_tenant_id, is_deleted, create_time, update_time, plan_comment_id,
        plan_comment_content, plan_tag_id, plan_tag_name, game_id, game_name, game_record_id,
        game_record_title, module_code, apply_level
    </sql>


    <select id="getListByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_point_record
        where 1=1
        and is_deleted = 0
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        <if test="row.planId != null">
            and plan_id = #{row.planId}
        </if>

        <if test="row.planIds != null and row.planIds.size() > 0">
            and plan_id in
            <foreach collection="row.planIds" item="planId" index="index"
                     open="(" close=")" separator=",">
                #{planId}
            </foreach>
        </if>

        <if test="row.scene != null">
            and scene = #{row.scene}
        </if>

        <if test="row.startTime != null">
            and create_time &gt;= #{row.startTime}
        </if>
        <if test="row.endTime != null">
            and create_time &lt;= #{row.endTime}
        </if>

        <if test="row.exceptChannelIds != null and row.exceptChannelIds.size() > 0">
            and !(1!=1
            <foreach collection="row.exceptChannelIds" item="exceptChannelId" index="index">
                or channel = #{exceptChannelId}
            </foreach>
            )
        </if>
        <if test="row.studentId != null">
            and student_id = #{row.studentId}
        </if>
        <if test="row.userId != null">
            and user_id = #{row.userId}
        </if>


    </select>

    <update id="delByStudentIds">
        update xs_point_record
        set is_deleted = 1,
        update_time = #{updateTime}
        where 1=1
        <if test="saasClassId != null">
            and saas_class_id = #{saasClassId}
        </if>
        <if test="studentIds != null and studentIds.size() > 0">
            and student_id in
            <foreach collection="studentIds" item="studentId" index="index"
                     open="(" close=")" separator=",">
                #{studentId}
            </foreach>
        </if>

    </update>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlqueue.dal.dao.XsStudentMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlqueue.dal.entity.XsStudent">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="student_id" jdbcType="BIGINT" property="studentId" />
    <result column="student_no" jdbcType="VARCHAR" property="studentNo" />
    <result column="student_name" jdbcType="VARCHAR" property="studentName" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="is_leader" jdbcType="BIT" property="isLeader" />
    <result column="is_expire" jdbcType="BIT" property="isExpire" />
    <result column="is_suspend" jdbcType="BIT" property="isSuspend" />
    <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId" />
    <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    student_id, student_no, student_name, `password`, is_leader, is_expire, is_suspend, 
    saas_school_id, saas_tenant_id, create_time, update_time
  </sql>

  <select id="getListByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_student
    where 1=1
    <if test="row.studentIds != null and row.studentIds.size() > 0">
      and student_id in
      <foreach collection="row.studentIds" item="studentId" index="index"
               open="(" close=")" separator=",">
        #{studentId}
      </foreach>
    </if>

    <if test="row.studentId != null">
      and student_id = #{row.studentId}
    </if>
  </select>

  <insert id="addBatch" parameterType="com.hailiang.edu.xsjlqueue.dal.entity.XsStudent">
    REPLACE  into xs_student (student_id,
    student_no,
    student_name,
    `password`,
    is_leader,
    is_expire,
    is_suspend,
    saas_school_id,
    saas_tenant_id,
    create_time,
    update_time
    )
    values
    <foreach collection="studentList" item="row" index= "index" separator =",">
      (
      #{row.studentId},
      #{row.studentNo},
      #{row.studentName},
      #{row.password},
      #{row.isLeader},
      #{row.isExpire},
      #{row.isSuspend},
      #{row.saasSchoolId},
      #{row.saasTenantId},
      #{row.createTime},
      #{row.updateTime}
      )
    </foreach >
  </insert>

  <update id="batchUpdateById">
    <foreach collection="xsStudentList" item="xsStudent" separator=";">
      update
      xs_student
      <set>
        <if test="xsStudent.studentNo != null">
          student_no = #{xsStudent.studentNo},
        </if>
        <if test="xsStudent.studentName != null">
          student_name = #{xsStudent.studentName},
        </if>
        <if test="xsStudent.isSuspend != null">
          is_suspend = #{xsStudent.isSuspend},
        </if>
        <if test="xsStudent.updateTime != null">
          update_time = #{xsStudent.updateTime},
        </if>
      </set>
      <where>
        student_id = #{xsStudent.studentId}
      </where>
    </foreach>
  </update>

</mapper>
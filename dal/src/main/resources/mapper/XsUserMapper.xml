<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlqueue.dal.dao.XsUserMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlqueue.dal.entity.XsUser">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="avatar" jdbcType="VARCHAR" property="avatar" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="last_login_time" jdbcType="INTEGER" property="lastLoginTime" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    user_id, user_name, `password`, email, phone, avatar, account_name, last_login_time, 
    is_deleted, create_time, update_time
  </sql>

  <select id="getListByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_user
    where 1=1
    and is_deleted = 0
    <if test="row.userId != null">
      and user_id = #{row.userId}
    </if>
    <if test="row.userIds != null and row.userIds.size() > 0">
      and user_id in
      <foreach collection="row.userIds" item="userId" index="index"
               open="(" close=")" separator=",">
        #{userId}
      </foreach>
    </if>
  </select>

  <select id="getRowByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_user
    where 1=1
    and is_deleted = 0
    <if test="row.userId != null">
      and user_id = #{row.userId}
    </if>
    <if test="row.userIds != null and row.userIds.size() > 0">
      and user_id in
      <foreach collection="row.userIds" item="userId" index="index"
               open="(" close=")" separator=",">
        #{userId}
      </foreach>
    </if>
    limit 1
  </select>


</mapper>
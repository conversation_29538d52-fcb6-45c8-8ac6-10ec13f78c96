<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlqueue.dal.dao.XsSmsReportStudentMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlqueue.dal.entity.XsSmsReportStudent">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="sms_report_id" jdbcType="BIGINT" property="smsReportId"/>
        <result column="student_id" jdbcType="BIGINT" property="studentId"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId"/>
        <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId"/>
        <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        id, sms_report_id, student_id, is_deleted, saas_class_id, saas_school_id, saas_tenant_id,
        create_time, update_time
    </sql>


    <insert id="batchAdd" parameterType="com.hailiang.edu.xsjlqueue.dal.entity.XsSmsReportStudent">
        insert into xs_sms_report_student (
        sms_report_id,
        student_id,
        saas_class_id,
        saas_school_id,
        saas_tenant_id,
        is_deleted,
        create_time,
        update_time
        )
        values
        <foreach collection="list" item="row" index="index" separator=",">
            (#{row.smsReportId},
            #{row.studentId},
            #{row.saasClassId},
            #{row.saasSchoolId},
            #{row.saasTenantId},
            #{row.isDeleted},
            #{row.createTime},
            #{row.updateTime}
            )
        </foreach>
    </insert>

    <select id="getOtherReportCount" resultType="int">
        select count(*)
        from xs_sms_report_student
        where 1=1
        and is_deleted = 0
        and sms_report_id != #{smsReportId}
        and student_id in
        <foreach collection="studentIds" item="studentId" index="index"
                 open="(" close=")" separator=",">
            #{studentId}
        </foreach>
    </select>

    <select id="getOtherReportStudent" resultType="java.lang.Long">
        select DISTINCT student_id
        from xs_sms_report_student
        where 1=1
        and is_deleted = 0
        and sms_report_id != #{smsReportId}
        and student_id in
        <foreach collection="studentIds" item="studentId" index="index"
                 open="(" close=")" separator=",">
            #{studentId}
        </foreach>
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlqueue.dal.dao.XsSysIconMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlqueue.dal.entity.XsSysIcon">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="url_big" jdbcType="VARCHAR" property="urlBig" />
    <result column="is_used" jdbcType="BIT" property="isUsed" />
    <result column="icon_type" jdbcType="BIT" property="iconType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, url, url_big, is_used, icon_type, create_time, update_time
  </sql>

  <select id="getListByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_sys_icon
    where 1=1
    <if test="row.isUsed != null">
      and is_used = #{row.isUsed}
    </if>
    <if test="row.iconType != null">
      and icon_type = #{row.iconType}
    </if>
  </select>
    <select id="getRowByCondition" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from xs_sys_icon
      where 1=1
      <if test="row.id != null">
        and id = #{row.id}
      </if>
      limit 1
    </select>


</mapper>
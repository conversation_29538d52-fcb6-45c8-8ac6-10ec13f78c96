<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlqueue.dal.dao.XsTaskMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlqueue.dal.entity.XsTask">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="plan_id" jdbcType="INTEGER" property="planId"/>
        <result column="business_id" jdbcType="BIGINT" property="businessId"/>
        <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId"/>
        <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId"/>
        <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        task_id, user_id, plan_id, business_id, business_type, is_deleted, saas_class_id,
        saas_school_id, saas_tenant_id, create_time, update_time
    </sql>

    <select id="getRowByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_task
        <where>
            <include refid="where"/>
        </where>
        limit 1
    </select>

    <sql id="where">
        is_deleted = 0
        <if test="row.businessId != null">
            and business_id = #{row.businessId}
        </if>
        <if test="row.businessType != null">
            and business_type = #{row.businessType}
        </if>
    </sql>
</mapper>
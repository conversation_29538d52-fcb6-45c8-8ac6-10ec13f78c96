<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlqueue.dal.dao.XsGroupStudentMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlqueue.dal.entity.XsGroupStudent">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="group_id" jdbcType="INTEGER" property="groupId" />
    <result column="student_id" jdbcType="BIGINT" property="studentId" />
    <result column="sort_val" jdbcType="INTEGER" property="sortVal" />
    <result column="is_group_leader" jdbcType="BIT" property="isGroupLeader" />
    <result column="is_small_group_leader" jdbcType="BIT" property="isSmallGroupLeader" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />

    <association property="xsGroup" javaType="com.hailiang.edu.xsjlqueue.dal.entity.XsGroup">
      <result column="group_name" jdbcType="VARCHAR" property="groupName" />
      <result column="icon" jdbcType="VARCHAR" property="icon" />
      <result column="plan_id" jdbcType="INTEGER" property="planId" />
      <result column="user_id" jdbcType="INTEGER" property="userId" />
      <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId" />
      <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId" />
      <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId" />
    </association>

  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, group_id, student_id, sort_val, is_group_leader, is_small_group_leader, create_time, 
    update_time
  </sql>

  <delete id="delByStudentIds">
    delete gs from xs_group_student as gs
    INNER JOIN xs_group as g on g.group_id = gs.group_id
    where
    1=1
    <if test="saasClassId != null">
      and g.saas_class_id = #{saasClassId}
    </if>
    <if test="studentIds != null and studentIds.size() > 0">
      and gs.student_id in
      <foreach collection="studentIds" item="id" index="index"
               open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </delete>

</mapper>
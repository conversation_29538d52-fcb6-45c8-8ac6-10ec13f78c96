<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlqueue.dal.dao.XsPlanUserMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlqueue.dal.entity.XsPlanUser">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="plan_id" jdbcType="INTEGER" property="planId"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="is_hand_admin" jdbcType="BIT" property="isHandAdmin"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>

        <association property="xsPlan" javaType="com.hailiang.edu.xsjlqueue.dal.entity.XsPlan">
            <result column="plan_id" jdbcType="VARCHAR" property="planId"/>
            <result column="plan_name" jdbcType="VARCHAR" property="planName"/>
            <result column="point_retail_rate" jdbcType="DECIMAL" property="pointRetailRate"/>
            <result column="plan_type" jdbcType="BIT" property="planType"/>
            <result column="group_type" jdbcType="VARCHAR" property="groupType"/>
            <result column="group_num" jdbcType="INTEGER" property="groupNum"/>
            <result column="is_used" jdbcType="BIT" property="isUsed"/>
            <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId"/>
            <result column="saas_class_name" jdbcType="VARCHAR" property="saasClassName"/>
            <result column="saas_class_alias" jdbcType="VARCHAR" property="saasClassAlias"/>
            <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId"/>
            <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId"/>
            <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
            <result column="xpuser_id" jdbcType="INTEGER" property="userId"/>
            <result column="xpcreate_time" jdbcType="TIMESTAMP" property="createTime"/>

        </association>

    </resultMap>
    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        id, plan_id, user_id, is_hand_admin, create_time, update_time
    </sql>


    <select id="getListByCondition" resultMap="BaseResultMap">
        select xpu.*,xp.plan_id,xp.plan_name,xp.point_retail_rate,xp.plan_type
        ,xp.saas_class_id,xp.saas_class_name,xp.saas_class_alias,xp.create_time as xpcreate_time,xp.user_id as xpuser_id
        ,xp.group_type,xp.group_num
        from xs_plan_user as xpu
        inner join xs_plan as xp on xp.plan_id = xpu.plan_id
        where 1=1
        and xp.is_deleted = 0
        and xp.is_used = 1
        <if test="row.planId != null">
            and xpu.plan_id = #{row.planId}
        </if>
        <if test="row.userId != null">
            and xpu.user_id = #{row.userId}
        </if>
        <if test="row.saasClassId != null">
            and xp.saas_class_id = #{row.saasClassId}
        </if>
        order by xpu.create_time asc
    </select>

</mapper>
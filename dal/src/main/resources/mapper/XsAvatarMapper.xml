<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlqueue.dal.dao.XsAvatarMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlqueue.dal.entity.XsAvatar">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="avatar_id" jdbcType="BIGINT" property="avatarId"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="business_id" jdbcType="BIGINT" property="businessId"/>
        <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
        <result column="data_type" jdbcType="VARCHAR" property="dataType"/>
        <result column="sys_icon_type" jdbcType="INTEGER" property="sysIconType"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId"/>
        <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId"/>
        <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        avatar_id, user_id, file_url, business_id, business_type, data_type, sys_icon_type,
        is_deleted, saas_class_id, saas_school_id, saas_tenant_id, create_time, update_time
    </sql>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into xs_avatar (avatar_id, user_id, file_url, business_id, business_type, data_type, sys_icon_type,
        is_deleted, saas_class_id, saas_school_id, saas_tenant_id, create_time, update_time
        )
        values
        <foreach collection ="xsAvatarList" item="xsAvatar" index= "index" separator =",">
            (
            #{xsAvatar.avatarId},
            #{xsAvatar.userId},
            #{xsAvatar.fileUrl},
            #{xsAvatar.businessId},
            #{xsAvatar.businessType},
            #{xsAvatar.dataType},
            #{xsAvatar.sysIconType},
            0,
            #{xsAvatar.saasClassId},
            #{xsAvatar.saasSchoolId},
            #{xsAvatar.saasTenantId},
            #{xsAvatar.createTime},
            #{xsAvatar.updateTime}
            )
        </foreach>
    </insert>

    <select id="getListByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM xs_avatar
        <where>
            <include refid="where"/>
        </where>
        order by create_time desc
    </select>

    <sql id="where">
        is_deleted = 0
        <if test="row.avatarId != null">
            and avatar_id = #{row.avatarId}
        </if>
        <if test="row.userId != null">
            and user_id = #{row.userId}
        </if>
        <if test="row.fileUrl != null">
            and file_url = #{row.fileUrl}
        </if>
        <if test="row.businessId != null">
            and business_id = #{row.businessId}
        </if>
        <if test="row.businessIds != null and row.businessIds.size() > 0">
            and business_id in
            <foreach collection="row.businessIds" item="businessId" index="index"
                     open="(" close=")" separator=",">
                #{businessId}
            </foreach>
        </if>
        <if test="row.businessType != null">
            and business_type = #{row.businessType}
        </if>
        <if test="row.dataType != null">
            and data_type = #{row.dataType}
        </if>
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        <if test="row.saasClassIds != null and row.saasClassIds.size() > 0">
            and saas_class_id in
            <foreach collection="row.saasClassIds" item="saasClassId" index="index"
                     open="(" close=")" separator=",">
                #{saasClassId}
            </foreach>
        </if>
    </sql>
</mapper>
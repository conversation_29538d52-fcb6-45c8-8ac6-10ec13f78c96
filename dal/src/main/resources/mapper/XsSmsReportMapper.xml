<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlqueue.dal.dao.XsSmsReportMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlqueue.dal.entity.XsSmsReport">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="sms_report_id" jdbcType="BIGINT" property="smsReportId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="send_hour" jdbcType="INTEGER" property="sendHour" />
    <result column="send_minute" jdbcType="INTEGER" property="sendMinute" />
    <result column="sms_rule_id" jdbcType="INTEGER" property="smsRuleId" />
    <result column="sms_content_range" jdbcType="VARCHAR" property="smsContentRange" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId" />
    <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId" />
    <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.hailiang.edu.xsjlqueue.dal.entity.XsSmsReport">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <result column="sms_rule_meta_data" jdbcType="LONGVARCHAR" property="smsRuleMetaData" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    sms_report_id, user_id, send_hour, send_minute, sms_rule_id, sms_content_range, start_time, 
    end_time, is_deleted, saas_class_id, saas_school_id, saas_tenant_id, create_time, 
    update_time
  </sql>
  <sql id="Blob_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    sms_rule_meta_data
  </sql>
</mapper>
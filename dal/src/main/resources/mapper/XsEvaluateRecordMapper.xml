<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlqueue.dal.dao.XsEvaluateRecordMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlqueue.dal.entity.XsEvaluateRecord">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="evaluate_record_id" jdbcType="BIGINT" property="evaluateRecordId"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="plan_id" jdbcType="INTEGER" property="planId"/>
        <result column="evaluate_name" jdbcType="VARCHAR" property="evaluateName"/>
        <result column="evaluate_type" jdbcType="VARCHAR" property="evaluateType"/>
        <result column="role_code" jdbcType="VARCHAR" property="roleCode"/>
        <result column="user_role_name" jdbcType="VARCHAR" property="userRoleName"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="evaluate_status" jdbcType="VARCHAR" property="evaluateStatus"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId"/>
        <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId"/>
        <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        evaluate_record_id, user_id, plan_id, evaluate_name, evaluate_type, start_time, end_time,
        evaluate_status, role_code,user_role_name, is_deleted, saas_class_id, saas_school_id, saas_tenant_id, create_time,
        update_time
    </sql>

    <select id="getListByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_evaluate_record
        <where>
            <include refid="where"/>
        </where>
    </select>

    <select id="getRowByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xs_evaluate_record
        <where>
            <include refid="where"/>
        </where>
        limit 1
    </select>

    <sql id="where">
        is_deleted = 0
        <if test="row.saasClassId != null">
            and saas_class_id = #{row.saasClassId}
        </if>
        <if test="row.planId != null and row.planId != ''">
            and plan_id = #{row.planId}
        </if>
        <if test="row.evaluateRecordId != null">
            and evaluate_record_id = #{row.evaluateRecordId}
        </if>
        <if test="row.startTime != null">
            and create_time >= #{row.startTime}
        </if>
        <if test="row.endTime != null">
            and create_time &lt;= #{row.endTime}
        </if>

        <if test="row.filterUnSave != null and row.filterUnSave == true">
            and evaluate_status != "unSave"
        </if>

        <if test="row.evaluateStatus != null and row.evaluateStatus != ''">
            and evaluate_status = #{row.evaluateStatus}
        </if>

        <if test="row.roleCode != null and row.roleCode != ''">
            and role_code = #{row.roleCode}
        </if>

        <if test="row.planIds != null and row.planIds.size() > 0">
            and plan_id in
            <foreach collection="row.planIds" item="planId" index="index"
                     open="(" close=")" separator=",">
                #{planId}
            </foreach>
        </if>

        <if test="row.userIds != null">
            <if test="row.userIds.size() > 0">
                and user_id in
                <foreach collection="row.userIds" item="userId" index="index"
                         open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="row.userIds.size() == 0">
                and 1 = 0
            </if>
        </if>
    </sql>
</mapper>
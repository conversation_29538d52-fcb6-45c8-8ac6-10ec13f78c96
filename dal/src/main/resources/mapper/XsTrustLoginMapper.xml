<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlqueue.dal.dao.XsTrustLoginMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlqueue.dal.entity.XsTrustLogin">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="trust_id" jdbcType="INTEGER" property="trustId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
    <result column="platform_code" jdbcType="VARCHAR" property="platformCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    trust_id, user_id, open_id, platform_code, create_time, update_time
  </sql>


  <select id="getListByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_trust_login
    where 1=1
    <if test="row.userIds != null and row.userIds.size() > 0">
      and user_id in
      <foreach collection="row.userIds" item="userId" index="index"
               open="(" close=")" separator=",">
        #{userId}
      </foreach>
    </if>

    <if test="row.platformCode != null">
      and platform_code = #{row.platformCode}
    </if>
  </select>


</mapper>
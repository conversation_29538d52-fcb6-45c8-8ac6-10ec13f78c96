<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlqueue.dal.dao.XsSmsRuleMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlqueue.dal.entity.XsSmsRule">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="sms_rule_id" jdbcType="INTEGER" property="smsRuleId" />
    <result column="sms_rule_name" jdbcType="VARCHAR" property="smsRuleName" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="edit_user_id" jdbcType="INTEGER" property="editUserId" />
    <result column="is_enabled" jdbcType="BIT" property="isEnabled" />
    <result column="data_range" jdbcType="VARCHAR" property="dataRange" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="time_type" jdbcType="VARCHAR" property="timeType" />
    <result column="day_number" jdbcType="INTEGER" property="dayNumber" />
    <result column="begin_day" jdbcType="TIMESTAMP" property="beginDay" />
    <result column="send_hour" jdbcType="INTEGER" property="sendHour" />
    <result column="send_minute" jdbcType="INTEGER" property="sendMinute" />
    <result column="is_contain_help" jdbcType="BIT" property="isContainHelp" />
    <result column="sms_content_range" jdbcType="VARCHAR" property="smsContentRange" />
    <result column="rule_type" jdbcType="VARCHAR" property="ruleType"/>
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId" />
    <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId" />
    <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    sms_rule_id, sms_rule_name, user_id, edit_user_id, is_enabled, data_range, plan_id,
    time_type, day_number, begin_day, send_hour, send_minute, is_contain_help, sms_content_range,
    rule_type, start_time, end_time,
    is_deleted, saas_class_id, saas_school_id, saas_tenant_id, create_time, update_time
  </sql>


  <select id="getRowByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_sms_rule
    where 1=1
    and is_deleted = 0
    <if test="row.smsRuleId != null">
      and sms_rule_id = #{row.smsRuleId}
    </if>
    <if test="row.isEnabled != null">
      and is_enabled = #{row.isEnabled}
    </if>
    <if test="row.sendHour != null">
      and send_hour = #{row.sendHour}
    </if>
    <if test="row.sendMinute != null">
      and send_minute = #{row.sendMinute}
    </if>
    <if test="row.saasClassIds != null and row.saasClassIds.size() > 0">
      and saas_class_id in
      <foreach collection="row.saasClassIds" item="saasClassId" index="index"
               open="(" close=")" separator=",">
        #{saasClassId}
      </foreach>
    </if>
    limit 1
  </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlqueue.dal.dao.XsTaskDetailMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlqueue.dal.entity.XsTaskDetail">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="task_detail_id" jdbcType="BIGINT" property="taskDetailId" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="assignee_id" jdbcType="BIGINT" property="assigneeId" />
    <result column="assignee_type" jdbcType="VARCHAR" property="assigneeType" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    task_detail_id, task_id, assignee_id, assignee_type, `status`, is_deleted, create_time, 
    update_time
  </sql>


  <select id="getRowByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from xs_task_detail
    <where>
      <include refid="where"/>
    </where>
    limit 1
  </select>

  <sql id="where">
    is_deleted = 0
    <if test="row.taskId != null">
      and task_id = #{row.taskId}
    </if>
    <if test="row.assigneeId != null">
      and assignee_id = #{row.assigneeId}
    </if>
    <if test="row.assigneeType != null">
      and assignee_type = #{row.assigneeType}
    </if>
    <if test="row.status != null">
      and status = #{row.status}
    </if>
  </sql>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlqueue.dal.dao.XsPlanTagMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlqueue.dal.entity.XsPlanTag">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="plan_tag_id" jdbcType="BIGINT" property="planTagId" />
    <result column="plan_tag_name" jdbcType="VARCHAR" property="planTagName" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="sort_val" jdbcType="INTEGER" property="sortVal" />
    <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId" />
    <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId" />
    <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="comment_template_id" jdbcType="INTEGER" property="commentTemplateId" />
    <result column="apply_level" jdbcType="VARCHAR" property="applyLevel" />
    <result column="module_code" jdbcType="INTEGER" property="moduleCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    plan_tag_id, plan_tag_name, plan_id, user_id, sort_val, saas_class_id, saas_school_id, 
    saas_tenant_id, is_deleted, create_time, update_time, comment_template_id, apply_level, 
    module_code
  </sql>


  <select id="getListByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xs_plan_tag
    where 1=1
    and is_deleted = 0
    <if test="row.saasClassId != null">
      and saas_class_id = #{row.saasClassId}
    </if>
    <if test="row.planId != null">
      and plan_id = #{row.planId}
    </if>

  </select>

</mapper>
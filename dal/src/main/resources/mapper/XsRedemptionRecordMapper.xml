<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.edu.xsjlqueue.dal.dao.XsRedemptionRecordMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.edu.xsjlqueue.dal.entity.XsRedemptionRecord">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="redemption_record_id" jdbcType="INTEGER" property="redemptionRecordId"/>
        <result column="student_id" jdbcType="BIGINT" property="studentId"/>
        <result column="student_name" jdbcType="VARCHAR" property="studentName"/>
        <result column="prize_id" jdbcType="INTEGER" property="prizeId"/>
        <result column="prize_name" jdbcType="VARCHAR" property="prizeName"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="score" jdbcType="DECIMAL" property="score"/>
        <result column="plan_id" jdbcType="INTEGER" property="planId"/>
        <result column="point_record_id" jdbcType="BIGINT" property="pointRecordId"/>
        <result column="is_revoke" jdbcType="BIT" property="isRevoke"/>
        <result column="saas_class_id" jdbcType="VARCHAR" property="saasClassId"/>
        <result column="saas_school_id" jdbcType="VARCHAR" property="saasSchoolId"/>
        <result column="saas_tenant_id" jdbcType="VARCHAR" property="saasTenantId"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        redemption_record_id, student_id, student_name, prize_id, prize_name, user_id, score,
        plan_id, point_record_id, is_revoke, saas_class_id, saas_school_id, saas_tenant_id,
        is_deleted, create_time, update_time
    </sql>

    <update id="delByStudentIds">
        update xs_redemption_record
        set is_deleted = 1,
        update_time = #{updateTime}
        where 1=1
        <if test="saasClassId != null">
            and saas_class_id = #{saasClassId}
        </if>
        <if test="studentIds != null and studentIds.size() > 0">
            and student_id in
            <foreach collection="studentIds" item="studentId" index="index"
                     open="(" close=")" separator=",">
                #{studentId}
            </foreach>
        </if>

    </update>

</mapper>
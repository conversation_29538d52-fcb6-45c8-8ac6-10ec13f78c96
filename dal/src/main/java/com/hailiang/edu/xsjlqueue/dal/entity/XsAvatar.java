package com.hailiang.edu.xsjlqueue.dal.entity;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.edu.xsjlqueue.consts.DeletedConst;
import lombok.Data;

/**
 * Table: xs_avatar
 */
@Data
@TableName("xs_avatar")
public class XsAvatar {
    /**
     * Column: avatar_id
     * Type: BIGINT
     * Default value: 0
     * Remark: 文件id
     */
    @TableId(type = IdType.INPUT)
    private Long avatarId;

    /**
     * Column: user_id
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 用户id
     */
    private Long userId;

    /**
     * Column: file_url
     * Type: VARCHAR(300)
     * Remark: 文件地址
     */
    private String fileUrl;


    /**
     * Column: business_id
     * Type: BIGINT
     * Default value: 0
     * Remark: 业务id
     */
    private Long businessId;

    /**
     * Column: business_type
     * Type: VARCHAR(50)
     * Remark: 业务类型 planGroup,student
     */
    private String businessType;

    /**
     * Column: data_type
     * Type: VARCHAR(50)
     * Default value: system
     * Remark: 数据分类 custom(自定义),system(系统)
     */
    private String dataType;

    /**
     * 头像类型 1|组 2|水果学生头像 3|奖品 4|奖状 5|怪兽学生头像 目前仅在dataType=system时有效
     */
    private Integer sysIconType;

    /**
     * Column: is_deleted
     * Type: BIT
     * Default value: 0
     * Remark: 是否删除 0|否 1|是
     */
    private Integer isDeleted;

    /**
     * Column: saas_class_id
     * Type: VARCHAR(100)
     * Remark: saas班级id
     */
    private String saasClassId;

    /**
     * Column: saas_school_id
     * Type: VARCHAR(100)
     * Remark: saas学校id
     */
    private String saasSchoolId;

    /**
     * Column: saas_tenant_id
     * Type: VARCHAR(100)
     * Remark: saas租户id
     */
    private String saasTenantId;

    /**
     * Column: create_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private String updateTime;

    public void init() {
        this.createTime = DateUtil.now();
        this.updateTime = DateUtil.now();
        this.isDeleted = DeletedConst.NO;
    }
}
package com.hailiang.edu.xsjlqueue.dal.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsUser;
import com.hailiang.edu.xsjlqueue.query.UserQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface XsUserMapper extends BaseMapper<XsUser> {

    List<XsUser> getListByCondition(@Param("row") UserQuery userQuery);

    XsUser getRowByCondition(@Param("row") UserQuery userQuery);

}
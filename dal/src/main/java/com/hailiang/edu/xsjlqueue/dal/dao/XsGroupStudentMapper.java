package com.hailiang.edu.xsjlqueue.dal.dao;


import java.util.Collection;

import com.hailiang.edu.xsjlqueue.dal.entity.XsGroupStudent;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

public interface XsGroupStudentMapper extends BaseMapper<XsGroupStudent> {

    int delByStudentIds(@Param("studentIds") Collection<Long> studentIds, @Param("saasClassId") String saasClassId);
}
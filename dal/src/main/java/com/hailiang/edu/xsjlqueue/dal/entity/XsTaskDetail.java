package com.hailiang.edu.xsjlqueue.dal.entity;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.edu.xsjlqueue.consts.DeletedConst;
import lombok.Data;

/**
 * Table: xs_task_detail
 */
@Data
@TableName("xs_task_detail")
public class XsTaskDetail {
    /**
     * Column: task_detail_id
     * Type: BIGINT
     * Default value: 0
     * Remark: 任务详情id
     */
    @TableId(type = IdType.INPUT)
    private Long taskDetailId;

    /**
     * Column: task_id
     * Type: BIGINT
     * Default value: 0
     * Remark: 任务id
     */
    private Long taskId;

    /**
     * Column: assignee_id
     * Type: BIGINT
     * Default value: 0
     * Remark: 执行者id
     */
    private Long assigneeId;

    /**
     * Column: assignee_type
     * Type: VARCHAR(50)
     * Default value: student
     * Remark: 执行者类型  学生|student
     */
    private String assigneeType;

    /**
     * Column: status
     * Type: VARCHAR(50)
     * Default value: noExecute
     * Remark: 状态 未执行|noExecute 执行失败|fail 已执行|executed
     */
    private String status;

    /**
     * Column: is_deleted
     * Type: BIT
     * Default value: 0
     * Remark: 是否删除 0|否 1|是
     */
    private Integer isDeleted;

    /**
     * Column: create_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private String updateTime;

    public void init() {
        this.createTime = DateUtil.now();
        this.updateTime = DateUtil.now();
        this.isDeleted = DeletedConst.NO;
    }
}
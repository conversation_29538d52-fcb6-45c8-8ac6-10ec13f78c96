package com.hailiang.edu.xsjlqueue.dal.biz.mongodb;

import com.hailiang.edu.xsjlqueue.dal.entity.mongodb.AwardRecordOther;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Component
public class AwardRecordOtherStorage {

    @Resource
    private MongoTemplate mongoTemplate;

    /**
     * 批量插入
     * @param awardRecordOtherList
     */
    public void batchInsert(List<AwardRecordOther> awardRecordOtherList) {
        if (CollectionUtils.isEmpty(awardRecordOtherList)) {
            return;
        }
        mongoTemplate.insert(awardRecordOtherList, AwardRecordOther.class);
    }

    /**
     * 根据 id进行查询
     * @param awardRecordIds
     * @return
     */
    public List<AwardRecordOther> getListByAwardRecordIds(Set<Long> awardRecordIds) {
        if (CollectionUtils.isEmpty(awardRecordIds)) {
            return new ArrayList<>();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("awardRecordId").in(awardRecordIds));

        return mongoTemplate.find(query, AwardRecordOther.class);
    }







}

package com.hailiang.edu.xsjlqueue.dal.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsPlanTag;
import com.hailiang.edu.xsjlqueue.query.PlanTagQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface XsPlanTagMapper extends BaseMapper<XsPlanTag> {

    List<XsPlanTag> getListByCondition(@Param("row") PlanTagQuery planTagQuery);

}
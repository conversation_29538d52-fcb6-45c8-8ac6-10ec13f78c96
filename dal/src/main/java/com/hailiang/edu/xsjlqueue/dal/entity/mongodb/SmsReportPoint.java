package com.hailiang.edu.xsjlqueue.dal.entity.mongodb;


import com.alibaba.fastjson.annotation.JSONField;
import com.hailiang.edu.xsjlqueue.dto.sms.RoleDto;
import com.hailiang.edu.xsjlqueue.dto.sms.SubjectDto;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 *
 *  快照报告明细
 *
 */
@Data
@Document(collection = "smsReportPoint")
public class SmsReportPoint {

    /**
     * 主键
     */
    @JSONField(ordinal = 1)
    private Long smsReportPointId;

    /**
     * 报告id
     */
    @JSONField(ordinal = 2)
    private Long smsReportId;

    /**
     * 积分明细id
     */
    @JSONField(ordinal = 3)
    private Long xsPointRecordId;

    /**
     * Column: content
     * Remark: 明细描述
     */
    @JSONField(ordinal = 4)
    private String content;

    /**
     * Column: student_id
     * Remark: 学生id
     */
    @JSONField(ordinal = 5)
    private Long studentId;

    /**
     * Column: user_id
     * Remark: 用户id
     */
    @JSONField(ordinal = 6)
    private Long userId;

    /**
     * Column: account_name
     * Remark: 用户姓名
     */
    @JSONField(ordinal = 7)
    private String accountName;

    /**
     * Column: score
     * Remark: 分值
     */
    @JSONField(ordinal = 8)
    private Double score;

    /**
     * Column: plan_id
     * Remark: 方案id
     */
    @JSONField(ordinal = 9)
    private Long planId;

    /**
     * Column: channel
     * Remark: 积分来源渠道 1|点评项 2|游戏 3|奖品
     */
    @JSONField(ordinal = 10)
    private Integer channel;

    /**
     * Column: scene
     * Remark: 得分场景 personal|个人所得 help|帮扶所得
     */
    @JSONField(ordinal = 11)
    private String scene;

    /**
     * Column: saas_class_id
     * Remark: saas班级id
     */
    @JSONField(ordinal = 12)
    private String saasClassId;

    /**
     * Column: saas_school_id
     * Remark: saas学校id
     */
    @JSONField(ordinal = 13)
    private String saasSchoolId;

    /**
     * Column: saas_tenant_id
     * Remark: saas租户id
     */
    @JSONField(ordinal = 14)
    private String saasTenantId;


    /**
     * Column: create_time
     * Remark: 积分明细创建时间
     */
    @JSONField(ordinal = 16)
    private String createTime;

    /**
     * Column: update_time
     * Remark: 积分明细更新时间
     */
    @JSONField(ordinal = 17)
    private String updateTime;

    /**
     * Column: plan_comment_id
     * Remark: 方案点评项id
     */
    @JSONField(ordinal = 18)
    private String planCommentId;

    /**
     * Column: plan_comment_content
     * Remark: 点评项内容
     */
    @JSONField(ordinal = 19)
    private String planCommentContent;

    /**
     * Column: plan_tag_id
     * Remark: 方案标签id
     */
    @JSONField(ordinal = 20)
    private Long planTagId;

    /**
     * Column: plan_tag_name
     * Remark: 标签名称
     */
    @JSONField(ordinal = 21)
    private String planTagName;

    /**
     * Column: game_id
     * Remark: 游戏id
     */
    @JSONField(ordinal = 22)
    private Long gameId;

    /**
     * Column: game_name
     * Remark: 游戏名称
     */
    @JSONField(ordinal = 23)
    private String gameName;

    /**
     * Column: game_record_id
     * Remark: 游戏记录id
     */
    @JSONField(ordinal = 24)
    private Long gameRecordId;

    /**
     * Column: game_record_title
     * Remark: 游戏记录名称
     */
    @JSONField(ordinal = 25)
    private String gameRecordTitle;

    /**
     * Column: module_code
     * Remark: 综评 0|无 1|德 2|智 3|体 4|美 5|劳
     */
    @JSONField(ordinal = 26)
    private Integer moduleCode;

    /**
     * Column: apply_level
     * Remark: 应用级别 general|普通 school|校级
     */
    @JSONField(ordinal = 27)
    private String applyLevel;




    /**
     * 创建时间
     */
    @JSONField(ordinal = 28)
    private String gmtCreated;

    /**
     * 创建秒时间戳
     */
    @JSONField(ordinal = 29)
    private Long gmtCreatedTimeStamp;

    /**
     * 更新时间
     */
    @JSONField(ordinal = 30)
    private String gmtModified;

    /**
     * 更新秒时间戳
     */
    @JSONField(ordinal = 31)
    private Long gmtModifiedTimeStamp;

    /**
     *  是否删除 0|否 1|是
     */
    @JSONField(ordinal = 32)
    private Integer isDeleted;


    @JSONField(ordinal = 33)
    private List<RoleDto> roleList;

    @JSONField(ordinal = 34)
    private List<SubjectDto> subjectList;


}

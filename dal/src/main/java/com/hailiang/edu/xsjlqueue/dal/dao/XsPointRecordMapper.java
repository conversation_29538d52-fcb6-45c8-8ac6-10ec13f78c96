package com.hailiang.edu.xsjlqueue.dal.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsPointRecord;
import com.hailiang.edu.xsjlqueue.query.PointRecordQuery;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;


public interface XsPointRecordMapper extends BaseMapper<XsPointRecord> {

    List<XsPointRecord> getListByCondition(@Param("row")PointRecordQuery pointRecordQuery);

    int delByStudentIds(@Param("studentIds") Collection<Long> studentIds, @Param("saasClassId") String saasClassId, @Param("updateTime") String updateTime);
}
package com.hailiang.edu.xsjlqueue.dal.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsTrustLogin;
import com.hailiang.edu.xsjlqueue.query.TrustLoginQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface XsTrustLoginMapper extends BaseMapper<XsTrustLogin> {

    List<XsTrustLogin> getListByCondition(@Param("row") TrustLoginQuery trustLoginQuery);

}
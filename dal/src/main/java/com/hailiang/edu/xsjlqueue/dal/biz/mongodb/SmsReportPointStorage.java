package com.hailiang.edu.xsjlqueue.dal.biz.mongodb;


import com.hailiang.edu.xsjlqueue.dal.entity.mongodb.SmsReportPoint;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Component
public class SmsReportPointStorage {

    @Resource
    private MongoTemplate mongoTemplate;


    public void batchInsert(List<SmsReportPoint> smsReportPointList) {
        if (CollectionUtils.isEmpty(smsReportPointList)) {
            return;
        }
        mongoTemplate.insert(smsReportPointList, SmsReportPoint.class);
    }
}

package com.hailiang.edu.xsjlqueue.dal.biz;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.hailiang.edu.xsjlqueue.consts.CommonConst;
import com.hailiang.edu.xsjlqueue.dal.dao.XsSysIconMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsSysIcon;
import com.hailiang.edu.xsjlqueue.query.SysIconQuery;
import com.hailiang.edu.xsjlqueue.util.RedisUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONObject;

@Component
public class StudentIconStorage {

    private final String studentFormat = "icon:monster:student";

    //每个班级一个集合 set 类型
    private final String classKeyFormat = "icon:monster:classId:%s";


    @Resource
    RedisUtil redisUtil;

    @Resource
    XsSysIconMapper xsSysIconMapper;
    /**
     * 随机获取一个班级下的学生的一个头像地址 (需要线程安全)
     * @param saasClassId
     * @return
     */
    public synchronized  String getStudentRandomIcon(String saasClassId) {

        try {
            String key = String.format(classKeyFormat, saasClassId);

            Object obj = redisUtil.sPop(key);
            if (obj == null) {
                List<XsSysIcon> xsSysIconList = getStudentIconList();
                if (!CollectionUtils.isEmpty(xsSysIconList)) {
                    String[] urls = xsSysIconList.stream().map(XsSysIcon::getUrl).collect(Collectors.toList())
                            .toArray(new String[xsSysIconList.size()]);
                    redisUtil.sSet(key, urls);

                    return getStudentRandomIcon(saasClassId);
                }

            } else {
                return obj.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "";
    }

    public List<XsSysIcon> getStudentIconList(){
        try {
            if(redisUtil.hasKey(studentFormat)){
                String json = redisUtil.get(studentFormat).toString();
                return JSONObject.parseArray(json,XsSysIcon.class);
            }else{
                SysIconQuery sysIconQuery = new SysIconQuery();
                sysIconQuery.setIsUsed(1);
                sysIconQuery.setIconType(CommonConst.ICON_TYPE_MONSTER);
                List<XsSysIcon> xsSysIconList = xsSysIconMapper.getListByCondition(sysIconQuery);
                redisUtil.set(studentFormat, JSONObject.toJSONString(xsSysIconList),60000);
                return xsSysIconList;
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return new ArrayList<>();
    }



}

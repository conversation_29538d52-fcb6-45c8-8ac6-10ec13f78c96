package com.hailiang.edu.xsjlqueue.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Table: xs_sms_rule
 */
@Data
@TableName("xs_sms_rule")
public class XsSmsRule {
    /**
     * Column: sms_rule_id
     * Remark: 主键
     */
    @TableId(type = IdType.AUTO)
    private Long smsRuleId;

    /**
     * Column: sms_rule_name
     * Remark: 推送规则名称
     */
    private String smsRuleName;

    /**
     * Column: user_id
     * Remark: 创建人id
     */
    private Long userId;

    /**
     * Column: edit_user_id
     * Remark: 编辑人id
     */
    private Long editUserId;

    /**
     * Column: is_enabled
     * Remark: 是否启用 0|否 1|是
     * @see com.hailiang.edu.xsjlqueue.consts.EnabledConst
     */
    private Integer isEnabled;

    /**
     * Column: data_range
     * Remark: 推送数据范围 (class｜全班 ，plan｜当前方案)
     * @see com.hailiang.edu.xsjlqueue.consts.DataRangeConst
     */
    private String dataRange;

    /**
     * Column: plan_id
     * Remark: 方案id
     */
    private Long planId;

    /**
     * Column: time_type
     * Remark: 周期 week|周,month|月,define|自定义
     * @see com.hailiang.edu.xsjlqueue.consts.TimeTypeConst
     */
    private String timeType;

    /**
     * Column: day_number
     * Remark: 天序号 从1开始
     */
    private Integer dayNumber;

    /**
     * Column: begin_day
     * Remark: 开始日期(仅 time_type 自定义 有效)
     */
    private String beginDay;

    /**
     * Column: send_hour
     * Remark: 具体报告发送小时 0-23
     */
    private Integer sendHour;

    /**
     * Column: send_minute
     * Remark: 具体报告发送分钟 0-59
     */
    private Integer sendMinute;

    /**
     * Column: is_contain_help
     * Remark: 是否包含帮扶积分 0|不包含 1|包含
     * @see com.hailiang.edu.xsjlqueue.consts.ContainHelpConst
     */
    private Integer isContainHelp;

    /**
     * Column: sms_content_range
     * Remark: 推送内容范围 （1|表现明细 2|本期表现 3|行为分析 4|综合发展 5|获奖记录 逗号隔开存储）
     */
    private String smsContentRange;

    /**
     * 规则类型 once|单次 period|定期
     */
    private String ruleType;

    /**
     * 开始日期(仅 单次推送有效)
     */
    private String startTime;

    /**
     * 结束日期(仅 单次推送有效)
     */
    private String endTime;

    /**
     * Column: is_deleted
     * Remark: 是否删除 0|否 1|是
     */
    private Integer isDeleted;

    /**
     * Column: saas_class_id
     * Remark: saas班级id
     */
    private String saasClassId;

    /**
     * Column: saas_school_id
     * Remark: saas学校id
     */
    private String saasSchoolId;

    /**
     * Column: saas_tenant_id
     * Remark: saas租户id
     */
    private String saasTenantId;

    /**
     * Column: create_time
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Remark: 更新时间
     */
    private String updateTime;
}
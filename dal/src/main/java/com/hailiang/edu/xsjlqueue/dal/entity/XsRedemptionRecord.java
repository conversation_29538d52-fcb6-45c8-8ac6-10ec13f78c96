package com.hailiang.edu.xsjlqueue.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * Table: xs_redemption_record
 */
@Data
@TableName("xs_redemption_record")
public class XsRedemptionRecord {
    /**
     * Column: redemption_record_id
     * Remark: 主键
     */
    @TableId(type = IdType.AUTO)
    private Long redemptionRecordId;

    /**
     * Column: student_id
     * Remark: 学生id
     */
    private Long studentId;

    /**
     * Column: student_name
     * Remark: 学生姓名
     */
    private String studentName;

    /**
     * Column: prize_id
     * Remark: 奖品id
     */
    private Long prizeId;

    /**
     * Column: prize_name
     * Remark: 奖品名称
     */
    private String prizeName;

    /**
     * Column: user_id
     * Remark: 用户id
     */
    private Long userId;

    /**
     * Column: score
     * Remark: 分值
     */
    private Double score;

    /**
     * Column: plan_id
     * Remark: 方案id
     */
    private Long planId;

    /**
     * Column: point_record_id
     * Remark: 积分明细id
     */
    private Long pointRecordId;

    /**
     * Column: is_revoke
     * Remark: 是否撤销 0|否 1|是
     */
    private Integer isRevoke;

    /**
     * Column: saas_class_id
     * Remark: saas班级id
     */
    private String saasClassId;

    /**
     * Column: saas_school_id
     * Remark: saas学校id
     */
    private String saasSchoolId;

    /**
     * Column: saas_tenant_id
     * Remark: saas租户id
     */
    private String saasTenantId;

    /**
     * Column: is_deleted
     * Remark: 是否删除 0|否 1|是
     */
    private Integer isDeleted;

    /**
     * Column: create_time
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Remark: 更新时间
     */
    private String updateTime;
}
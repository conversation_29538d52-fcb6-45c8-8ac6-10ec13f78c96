package com.hailiang.edu.xsjlqueue.dal.biz;

import cn.hutool.crypto.SecureUtil;
import com.hailiang.edu.xsjlqueue.util.LogFormatUtil;
import com.hailiang.edu.xsjlqueue.util.RedisUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class SmsLinkSignStorage {

    /**
     * hash 类型
     * 外层key linkSign:值
     * 内层key smsReportId 报告id
     * 内层key studentId 学生id
     */
    private final String cacheKeyFormat = "linkSign:%s";

    @Resource
    RedisUtil redisUtil;
    @Resource
    LogFormatUtil logFormatUtil;


    public String setGetSmsLinkSign(Long smsReportId, Long studentId) {

        try {
            String linkSign = SecureUtil.md5(smsReportId + "sign" + studentId).toUpperCase();
            String key = String.format(cacheKeyFormat,linkSign);
            redisUtil.hset(key, "smsReportId", smsReportId);
            redisUtil.hset(key, "studentId", studentId);
            long seconds = 60 * 60 * 24 * 180;
            redisUtil.expire(key, seconds);

            return linkSign;
        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);

        }
        return "";
    }


}

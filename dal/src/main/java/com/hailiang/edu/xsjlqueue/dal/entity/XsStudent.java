package com.hailiang.edu.xsjlqueue.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Table: xs_student
 */
@Data
@TableName("xs_student")
public class XsStudent {
    /**
     * Column: student_id
     * Remark: 学生id
     */
    @TableId(type = IdType.INPUT)
    private Long studentId;

    /**
     * Column: student_no
     * Remark: 学号
     */
    private String studentNo;

    /**
     * Column: student_name
     * Remark: 学生姓名
     */
    private String studentName;

    /**
     * Column: password
     * Remark: 密码
     */
    private String password;

    /**
     * Column: is_leader
     * Remark: 是否班干 0|否 1|是
     */
    private Integer isLeader;

    /**
     * Column: is_expire
     * Remark: 是否体验学生 0|否 1|是
     */
    private Integer isExpire;

    /**
     * Column: is_suspend
     * Remark: 是否休学 0|否 1|是
     */
    private Integer isSuspend;

    /**
     * Column: saas_school_id
     * Remark: saas学校id
     */
    private String saasSchoolId;

    /**
     * Column: saas_tenant_id
     * Remark: saas租户id
     */
    private String saasTenantId;

    /**
     * Column: create_time
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Remark: 更新时间
     */
    private String updateTime;
}
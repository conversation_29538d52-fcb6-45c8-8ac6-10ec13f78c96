package com.hailiang.edu.xsjlqueue.dal.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsTask;
import com.hailiang.edu.xsjlqueue.dal.entity.XsTaskDetail;
import com.hailiang.edu.xsjlqueue.query.TaskDetailQuery;
import org.apache.ibatis.annotations.Param;

public interface XsTaskDetailMapper extends BaseMapper<XsTaskDetail> {

    XsTaskDetail getRowByCondition(@Param("row") TaskDetailQuery taskDetailQuery);

}
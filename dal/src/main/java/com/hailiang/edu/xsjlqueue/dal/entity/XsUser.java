package com.hailiang.edu.xsjlqueue.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Table: xs_user
 */
@Data
@TableName("xs_user")
public class XsUser {
    /**
     * Column: user_id
     * Remark: 用户id
     */
    @TableId(type = IdType.AUTO)
    private Long userId;

    /**
     * Column: user_name
     * Remark: 用户名
     */
    private String userName;

    /**
     * Column: password
     * Remark: 密码
     */
    private String password;

    /**
     * Column: email
     * Remark: 电子邮箱
     */
    private String email;

    /**
     * Column: phone
     * Remark: 手机号
     */
    private String phone;

    /**
     * Column: avatar
     * Remark: 用户头像地址
     */
    private String avatar;

    /**
     * Column: account_name
     * Remark: 用户姓名
     */
    private String accountName;

    /**
     * Column: last_login_time
     * Remark: 最近登录秒时间戳
     */
    private Long lastLoginTime;

    /**
     * Column: is_deleted
     * Remark: 是否删除 0|否 1|是
     */
    private Integer isDeleted;

    /**
     * Column: create_time
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Remark: 更新时间
     */
    private String updateTime;

}
package com.hailiang.edu.xsjlqueue.dal.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsClassStudent;
import com.hailiang.edu.xsjlqueue.query.ClassStudentQuery;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;


public interface XsClassStudentMapper extends BaseMapper<XsClassStudent> {

    List<XsClassStudent> getListByClassId(@Param("saasClassId") String saasClassId);

    List<XsClassStudent> getListByCondition(@Param("row") ClassStudentQuery classStudentQuery);

    int addBatch(@Param("list") List<XsClassStudent> classStudentList);

    int deleteByIds(@Param("ids") Collection<Long> ids);

}
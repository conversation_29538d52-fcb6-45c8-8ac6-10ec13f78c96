package com.hailiang.edu.xsjlqueue.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Table: xs_class_student
 */
@Data
@TableName("xs_class_student")
public class XsClassStudent {
    /**
     * Column: id
     * Remark: 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * Column: saas_class_id
     * Remark: saas班级id
     */
    private String saasClassId;

    /**
     * Column: student_id
     * Remark: 学生id
     */
    private Long studentId;

    /**
     * Column: url
     * Remark: 头像地址
     */
    private String url;

    /**
     * Column: saas_school_id
     * Remark: saas学校id
     */
    private String saasSchoolId;

    /**
     * Column: saas_tenant_id
     * Remark: saas租户id
     */
    private String saasTenantId;

    /**
     * Column: create_time
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Remark: 更新时间
     */
    private String updateTime;

    @TableField(exist = false)
    private XsStudent xsStudent;
}
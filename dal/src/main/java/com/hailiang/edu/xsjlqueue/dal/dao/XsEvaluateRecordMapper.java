package com.hailiang.edu.xsjlqueue.dal.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsEvaluateRecord;
import com.hailiang.edu.xsjlqueue.query.EvaluateRecordQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface XsEvaluateRecordMapper extends BaseMapper<XsEvaluateRecord> {

    List<XsEvaluateRecord> getListByCondition(@Param("row") EvaluateRecordQuery evaluateRecordQuery);

    XsEvaluateRecord getRowByCondition(@Param("row") EvaluateRecordQuery evaluateRecordQuery);

}
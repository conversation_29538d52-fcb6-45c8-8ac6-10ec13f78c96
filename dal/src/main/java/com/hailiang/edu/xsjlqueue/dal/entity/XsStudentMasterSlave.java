package com.hailiang.edu.xsjlqueue.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * Table: xs_student_master_slave
 */
@Data
@TableName("xs_student_master_slave")
public class XsStudentMasterSlave {
    /**
     * Column: id
     * Remark: 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * Column: student_id
     * Remark: 学生id
     */
    private Long studentId;

    /**
     * Column: parent_id
     * Remark: 学生师傅id
     */
    private Long parentId;

    /**
     * Column: plan_id
     * Remark: 方案id
     */
    private Long planId;

    /**
     * Column: group_id
     * Remark: 组id
     */
    private Long groupId;

    /**
     * Column: saas_class_id
     * Remark: saas班级id
     */
    private String saasClassId;

    /**
     * Column: saas_school_id
     * Remark: saas学校id
     */
    private String saasSchoolId;

    /**
     * Column: saas_tenant_id
     * Remark: saas租户id
     */
    private String saasTenantId;

    /**
     * Column: create_time
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Remark: 更新时间
     */
    private String updateTime;
}
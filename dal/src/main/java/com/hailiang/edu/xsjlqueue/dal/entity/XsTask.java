package com.hailiang.edu.xsjlqueue.dal.entity;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.edu.xsjlqueue.consts.DeletedConst;
import lombok.Data;

/**
 * Table: xs_task
 */
@Data
@TableName("xs_task")
public class XsTask {
    /**
     * Column: task_id
     * Type: BIGINT
     * Default value: 0
     * Remark: 任务id
     */
    @TableId(type = IdType.INPUT)
    private Long taskId;

    /**
     * Column: user_id
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 创建人id
     */
    private Long userId;

    /**
     * Column: plan_id
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 方案id
     */
    private Long planId;

    /**
     * Column: business_id
     * Type: BIGINT
     * Default value: 0
     * Remark: 业务id
     */
    private Long businessId;

    /**
     * Column: business_type
     * Type: VARCHAR(50)
     * Default value: evaluate
     * Remark: 业务类型  评语|evaluate
     */
    private String businessType;

    /**
     * Column: is_deleted
     * Type: BIT
     * Default value: 0
     * Remark: 是否删除 0|否 1|是
     */
    private Integer isDeleted;

    /**
     * Column: saas_class_id
     * Type: VARCHAR(100)
     * Remark: saas班级id
     */
    private String saasClassId;

    /**
     * Column: saas_school_id
     * Type: VARCHAR(100)
     * Remark: saas学校id
     */
    private String saasSchoolId;

    /**
     * Column: saas_tenant_id
     * Type: VARCHAR(100)
     * Remark: saas租户id
     */
    private String saasTenantId;

    /**
     * Column: create_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private String updateTime;

    public void init() {
        this.createTime = DateUtil.now();
        this.updateTime = DateUtil.now();
        this.isDeleted = DeletedConst.NO;
    }
}
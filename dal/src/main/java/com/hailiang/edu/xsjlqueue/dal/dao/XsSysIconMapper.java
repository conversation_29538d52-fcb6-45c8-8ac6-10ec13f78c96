package com.hailiang.edu.xsjlqueue.dal.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsSysIcon;
import com.hailiang.edu.xsjlqueue.query.SysIconQuery;
import org.apache.ibatis.annotations.Param;



public interface XsSysIconMapper extends BaseMapper<XsSysIcon> {

    List<XsSysIcon> getListByCondition(@Param("row") SysIconQuery sysIconQuery);

    XsSysIcon getRowByCondition(@Param("row") SysIconQuery sysIconQuery);
}
package com.hailiang.edu.xsjlqueue.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.hailiang.edu.xsjlqueue.consts.CommonConst;
import lombok.Data;

/**
 * Table: xs_sys_icon
 */
@Data
@TableName("xs_sys_icon")
public class XsSysIcon {
    /**
     * Column: id
     * Remark: 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * Column: url
     * Remark: 头像地址
     */
    private String url;

    /**
     * Column: url_big
     * Remark: 头像大图片
     */
    private String urlBig;

    /**
     * Column: is_used
     * Remark: 是否使用 0|否 1|是
     */
    private Integer isUsed;

    /**
     * Column: icon_type
     * Remark: 头像类型 1|组 2|学生 3|奖品 4|奖状 5|怪兽
     * @see CommonConst
     */
    private Integer iconType;

    /**
     * Column: create_time
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Remark: 更新时间
     */
    private String updateTime;
}
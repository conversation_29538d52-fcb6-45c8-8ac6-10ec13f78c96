package com.hailiang.edu.xsjlqueue.dal.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsEvaluateDetail;
import com.hailiang.edu.xsjlqueue.query.EvaluateDetailQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface XsEvaluateDetailMapper extends BaseMapper<XsEvaluateDetail> {

    /**
     * 根据条件查询 学生评语详情信息
     * @param evaluateDetailQuery
     * @return
     */
    List<XsEvaluateDetail> getListByCondition(@Param("row") EvaluateDetailQuery evaluateDetailQuery);

    XsEvaluateDetail getRowByCondition(@Param("row") EvaluateDetailQuery evaluateRecordQuery);

}
package com.hailiang.edu.xsjlqueue.dal.biz.mongodb;


import com.hailiang.edu.xsjlqueue.dal.entity.mongodb.SmsReportOther;
import com.hailiang.edu.xsjlqueue.dal.entity.mongodb.SmsReportPoint;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Component
public class SmsReportOtherStorage {

    @Resource
    private MongoTemplate mongoTemplate;


    public void batchInsert(List<SmsReportOther> smsReportOtherList) {
        if (CollectionUtils.isEmpty(smsReportOtherList)) {
            return;
        }
        mongoTemplate.insert(smsReportOtherList, SmsReportOther.class);
    }


}

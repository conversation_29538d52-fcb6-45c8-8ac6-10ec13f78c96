package com.hailiang.edu.xsjlqueue.dal.entity.mongodb;


import com.alibaba.fastjson.annotation.JSONField;
import com.hailiang.edu.xsjlqueue.consts.EvaluateStatusConst;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 *
 *  快照报告评语
 *
 */
@Data
@Document(collection = "smsReportStudentEvaluate")
public class SmsReportStudentEvaluate {

    /**
     * 主键
     */
    @JSONField(ordinal = 1)
    private Long smsReportEvaluateId;

    /**
     * 报告id
     */
    @JSONField(ordinal = 2)
    private Long smsReportId;


    @JSONField(ordinal = 4)
    private List<EvaluateDto> evaluateList;

    @JSONField(ordinal = 8)
    private String saasClassId;

    /**
     * Column: saas_school_id
     * Remark: saas学校id
     */
    @JSONField(ordinal = 9)
    private String saasSchoolId;

    /**
     * Column: saas_tenant_id
     * Remark: saas租户id
     */
    @JSONField(ordinal = 10)
    private String saasTenantId;

    /**
     * 创建时间
     */
    @JSONField(ordinal = 11)
    private String gmtCreated;

    /**
     * 创建秒时间戳
     */
    @JSONField(ordinal = 12)
    private Long gmtCreatedTimeStamp;

    /**
     * 更新时间
     */
    @JSONField(ordinal = 13)
    private String gmtModified;

    /**
     * 更新秒时间戳
     */
    @JSONField(ordinal = 14)
    private Long gmtModifiedTimeStamp;

    /**
     *  是否删除 0|否 1|是
     */
    @JSONField(ordinal = 15)
    private Integer isDeleted;

    @Data
    public static class EvaluateDto {

        /**
         * 评语记录id
         */
        private Long evaluateRecordId;

        /**
         * 创建人id
         */
        private Long userId;

        private Long studentId;

        /**
         * 方案id
         */
        private Long planId;

        /**
         * 评语名称
         */
        private String evaluateName;

        /**
         * 评语类型 期末|endTerm 日常|daily
         */
        private String evaluateType;

        /**
         * @see com.hailiang.edu.xsjlqueue.enums.RoleEnum
         */
        private String roleCode;

        /**
         * 评价身份
         */
        private String userRoleName;

        /**
         * 评语详情id
         */
        private Long evaluateDetailId;

        /**
         * 咒语提问内容
         */
        private String askData;

        /**
         * 评语内容
         */
        private String evaluateData;
    }
}

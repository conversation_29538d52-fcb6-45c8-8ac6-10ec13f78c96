package com.hailiang.edu.xsjlqueue.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Table: xs_award_record
 */
@Data
@TableName("xs_award_record")
public class XsAwardRecord {
    /**
     * Column: award_record_id
     * Type: BIGINT
     * Default value: 0
     * Remark: 奖项发布记录id
     */
    @TableId(type = IdType.INPUT)
    private Long awardRecordId;

    /**
     * Column: award_rule_id
     * Type: BIGINT
     * Default value: 0
     * Remark: 规则id
     */
    private Long awardRuleId;

    /**
     * Column: plan_id
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 方案id
     */
    private Long planId;

    /**
     * Column: start_time
     * Type: TIMESTAMP
     * Remark: 开始时间
     */
    private String startTime;

    /**
     * Column: end_time
     * Type: TIMESTAMP
     * Remark: 结束时间
     */
    private String endTime;

    /**
     * Column: user_id
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 用户id
     */
    private Long userId;

    /**
     * Column: rule_desc
     * Type: VARCHAR(300)
     * Remark: 规则描述
     */
    private String ruleDesc;

    /**
     * Column: is_deleted
     * Type: BIT
     * Default value: 0
     * Remark: 是否删除 0|否 1|是
     */
    private Integer isDeleted;

    /**
     * Column: saas_class_id
     * Type: VARCHAR(100)
     * Remark: saas班级id
     */
    private String saasClassId;

    /**
     * Column: saas_school_id
     * Type: VARCHAR(100)
     * Remark: saas学校id
     */
    private String saasSchoolId;

    /**
     * Column: saas_tenant_id
     * Type: VARCHAR(100)
     * Remark: saas租户id
     */
    private String saasTenantId;

    /**
     * Column: create_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private String updateTime;

    /**
     * 二维码地址
     */
    private String qrcodeUrl;
}
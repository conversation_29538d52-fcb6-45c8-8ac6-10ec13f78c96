/**
 * Hailiang.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.hailiang.edu.xsjlqueue.dal.biz;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.hailiang.edu.xsjlqueue.dal.entity.XsStudent;
import com.hailiang.edu.xsjlqueue.dto.saas.resp.StuSyncResultRespDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.hailiang.edu.xsjlqueue.dal.entity.XsClassStudent;
import com.hailiang.edu.xsjlqueue.dto.saas.resp.SaasSchoolRespDto;
import com.hailiang.edu.xsjlqueue.dto.stu.StudentDataSyncTaskDto;
import com.hailiang.edu.xsjlqueue.util.RedisUtil;

/**
 * <AUTHOR>
 * @version v0.1: StudentSyncStorage.java, v 0.1 2023年09月21日 20:25  zhousx Exp $
 */
@Component
public class StudentSyncStorage {

    private final String stuSyncKeyFormat = "stuSync:%s";

    private final String stuSyncDataKey = "stuSyncData";

    private final String stuSyncCountKey = "stuSyncCount";

    @Resource
    private RedisUtil redisUtil;

    private String getBigHashKey(Long syncJobId) {
        return String.format(stuSyncKeyFormat, syncJobId);
    }

    public void setStuSyncData(StudentDataSyncTaskDto taskDto, List<XsClassStudent> stuSyncData,
                               List<SaasSchoolRespDto> saasSchoolList) {
        if (CollectionUtils.isEmpty(stuSyncData)) {
            return;
        }

        // 1.<学校ID，班级学生数据>的映射关系
        Map<String, List<XsClassStudent>> schoolSyncClassMap = stuSyncData.stream()
                .collect(Collectors.groupingBy(XsClassStudent::getSaasSchoolId));

        String bigHashKey = this.getBigHashKey(taskDto.getSyncJobId());

        Object stuSyncObj = this.getStuSyncData(taskDto, stuSyncDataKey);
        List<StuSyncResultRespDto> syncClassStuList = Objects.isNull(stuSyncObj) ? Lists.newArrayList()
                : JSONObject.parseArray((String) stuSyncObj, StuSyncResultRespDto.class);

        if (CollectionUtils.isEmpty(saasSchoolList)) {
            return;
        }

        saasSchoolList.forEach(school -> {
            List<XsClassStudent> xsClassStudents = schoolSyncClassMap.get(String.valueOf(school.getId()));
            int updateClassCount = 0;
            if (CollectionUtils.isNotEmpty(xsClassStudents)) {
                updateClassCount = (int) xsClassStudents.stream().map(XsClassStudent::getSaasClassId).distinct().count();
            }
            if (redisUtil.hHasKey(bigHashKey, stuSyncDataKey)) {
                Map<Long, StuSyncResultRespDto> stuSyncMap = syncClassStuList.stream().collect(
                        Collectors.toMap(StuSyncResultRespDto::getSchoolId, Function.identity(), (p1, p2) -> p1));
                if (Objects.nonNull(stuSyncMap.get(school.getId()))) {
                    for (StuSyncResultRespDto stuSyncResultRespDto : syncClassStuList) {
                        if (stuSyncResultRespDto.getSchoolId().equals(school.getId())) {
                            stuSyncResultRespDto.setSyncClassCount(
                                    stuSyncResultRespDto.getSyncClassCount() + updateClassCount);
                        }
                    }
                } else {
                    StuSyncResultRespDto stuSyncRes = new StuSyncResultRespDto();
                    stuSyncRes.setSchoolId(school.getId());
                    stuSyncRes.setSchoolName(school.getSchoolName());
                    stuSyncRes.setSyncClassCount(updateClassCount);
                    syncClassStuList.add(stuSyncRes);
                }
                redisUtil.hset(bigHashKey, stuSyncDataKey,
                        JSONObject.toJSONString(syncClassStuList, SerializerFeature.DisableCircularReferenceDetect));
            } else {
                List<StuSyncResultRespDto> stuSyncResultRespDtos = Lists.newArrayList();
                StuSyncResultRespDto stuSyncResultRespDto = new StuSyncResultRespDto();
                stuSyncResultRespDto.setSchoolId(school.getId());
                stuSyncResultRespDto.setSchoolName(school.getSchoolName());
                stuSyncResultRespDto.setSyncClassCount(updateClassCount);
                stuSyncResultRespDtos.add(stuSyncResultRespDto);
                redisUtil.hset(bigHashKey, stuSyncDataKey, JSONObject.toJSONString(stuSyncResultRespDtos,
                        SerializerFeature.DisableCircularReferenceDetect));
            }
        });

    }

    public double setStuSyncCount(StudentDataSyncTaskDto taskDto) {
        String key = getBigHashKey(taskDto.getSyncJobId());
        return redisUtil.hincr(key, stuSyncCountKey, 1);
    }

    public Object getStuSyncData(StudentDataSyncTaskDto taskDto, String itemKey) {
        String key = getBigHashKey(taskDto.getSyncJobId());
        return redisUtil.hget(key, itemKey);
    }

    public void clearStuSyncInfo(StudentDataSyncTaskDto taskDto) {
        String key = getBigHashKey(taskDto.getSyncJobId());
        redisUtil.del(key);
    }

}
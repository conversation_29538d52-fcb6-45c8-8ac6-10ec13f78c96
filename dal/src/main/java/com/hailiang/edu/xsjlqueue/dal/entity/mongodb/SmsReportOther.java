package com.hailiang.edu.xsjlqueue.dal.entity.mongodb;

import com.alibaba.fastjson.annotation.JSONField;
import com.hailiang.edu.xsjlqueue.dto.sms.RoleDto;
import com.hailiang.edu.xsjlqueue.dto.sms.SubjectDto;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Set;

/**
 * 快照报告其他属性
 */
@Data
@Document(collection = "smsReportOther")
public class SmsReportOther {

    /**
     * 主键
     */
    @JSONField(ordinal = 1)
    private Long smsReportOtherId;

    /**
     * 报告id
     */
    @JSONField(ordinal = 2)
    private Long smsReportId;

    /**
     * 规则用户id
     */
    @JSONField(ordinal = 3)
    private Long userId;


    @JSONField(ordinal = 4)
    private List<PlanTagDto> planTagList;

    @Data
    public static class PlanTagDto {

        /**
         * 标签id
         */
        @JSONField(ordinal = 1)
        private Long planTagId;

        /**
         * Column: plan_tag_name
         * Remark: 标签名称
         */
        @JSONField(ordinal = 2)
        private String planTagName;

        /**
         * Column: plan_id
         * Remark: 方案id
         */
        @JSONField(ordinal = 3)
        private Long planId;

        /**
         * Column: user_id
         * Remark: 用户id
         */
        @JSONField(ordinal = 4)
        private Long userId;

        /**
         * Column: sort_val
         * Remark: 排序值
         */
        @JSONField(ordinal = 5)
        private Long sortVal;

        /**
         * Column: is_deleted
         * Remark: 是否删除 0|否 1|是
         */
        @JSONField(ordinal = 6)
        private Integer isDeleted;

        /**
         * Column: create_time
         * Remark: 创建时间
         */
        @JSONField(ordinal = 7)
        private String createTime;

        /**
         * Column: update_time
         * Remark: 更新时间
         */
        @JSONField(ordinal = 8)
        private String updateTime;

        /**
         * Column: comment_template_id
         * Remark: 点评项模版id
         */
        @JSONField(ordinal = 9)
        private Long commentTemplateId;

        /**
         * Column: apply_level
         * Remark: 应用级别 general|普通 school|校级
         */
        @JSONField(ordinal = 10)
        private String applyLevel;

        /**
         * Column: module_code
         * Remark: 综评 0|无 1|德 2|智 3|体 4|美 5|劳
         */
        @JSONField(ordinal = 11)
        private Integer moduleCode;

    }

    @JSONField(ordinal = 5)
    private List<StudentDto> studentList;

    @Data
    public static class StudentDto{


        /**
         * Column: student_id
         * Remark: 学生id
         */
        @JSONField(ordinal = 1)
        private Long studentId;

        /**
         * Column: student_no
         * Remark: 学号
         */
        @JSONField(ordinal = 2)
        private String studentNo;

        /**
         * Column: student_name
         * Remark: 学生姓名
         */
        @JSONField(ordinal = 3)
        private String studentName;

        /**
         * Column: url
         * Remark: 头像地址
         */
        @JSONField(ordinal = 4)
        private String url;

        /**
         * Column: is_leader
         * Remark: 是否班干 0|否 1|是
         */
        @JSONField(ordinal = 5)
        private Integer isLeader;


    }

    @JSONField(ordinal = 6)
    private List<RoleDto> roleList;


    @JSONField(ordinal = 7)
    private List<SubjectDto> subjectList;



    @JSONField(ordinal = 8)
    private String saasClassId;

    /**
     * Column: saas_school_id
     * Remark: saas学校id
     */
    @JSONField(ordinal = 9)
    private String saasSchoolId;

    /**
     * Column: saas_tenant_id
     * Remark: saas租户id
     */
    @JSONField(ordinal = 10)
    private String saasTenantId;

    /**
     * 创建时间
     */
    @JSONField(ordinal = 11)
    private String gmtCreated;

    /**
     * 创建秒时间戳
     */
    @JSONField(ordinal = 12)
    private Long gmtCreatedTimeStamp;

    /**
     * 更新时间
     */
    @JSONField(ordinal = 13)
    private String gmtModified;

    /**
     * 更新秒时间戳
     */
    @JSONField(ordinal = 14)
    private Long gmtModifiedTimeStamp;

    /**
     *  是否删除 0|否 1|是
     */
    @JSONField(ordinal = 15)
    private Integer isDeleted;

    /**
     *  报告关联的奖状记录ids
     */
    @JSONField(ordinal = 16)
    private Set<Long> awardRecordIds;
}

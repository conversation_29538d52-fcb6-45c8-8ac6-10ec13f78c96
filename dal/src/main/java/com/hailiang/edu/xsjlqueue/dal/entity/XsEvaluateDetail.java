package com.hailiang.edu.xsjlqueue.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * Table: xs_evaluate_detail
 */
@Data
public class XsEvaluateDetail {
    /**
     * Column: evaluate_detail_id
     * Type: BIGINT
     * Default value: 0
     * Remark: 评语详情id
     */
    @TableId(type = IdType.INPUT)
    private Long evaluateDetailId;

    /**
     * Column: evaluate_record_id
     * Type: BIGINT
     * Default value: 0
     * Remark: 评语记录id
     */
    private Long evaluateRecordId;

    /**
     * Column: student_id
     * Type: BIGINT
     * Default value: 0
     * Remark: 学生id
     */
    private Long studentId;

    /**
     * Column: student_name
     * Remark: 学生姓名
     */
    private String studentName;

    /**
     * Column: is_deleted
     * Type: BIT
     * Default value: 0
     * Remark: 是否删除 0|否 1|是
     */
    private Integer isDeleted;

    /**
     * Column: saas_class_id
     * Type: VARCHAR(100)
     * Remark: saas班级id
     */
    private String saasClassId;

    /**
     * Column: saas_school_id
     * Type: VARCHAR(100)
     * Remark: saas学校id
     */
    private String saasSchoolId;

    /**
     * Column: saas_tenant_id
     * Type: VARCHAR(100)
     * Remark: saas租户id
     */
    private String saasTenantId;

    /**
     * Column: create_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private String updateTime;

    /**
     * Column: ask_data
     * Type: LONGTEXT
     * Remark: 咒语提问内容
     */
    private String askData;

    /**
     * Column: evaluate_data
     * Type: LONGTEXT
     * Remark: 评语内容
     */
    private String evaluateData;
}
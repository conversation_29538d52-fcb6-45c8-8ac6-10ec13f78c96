package com.hailiang.edu.xsjlqueue.dal.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsPlanUser;
import com.hailiang.edu.xsjlqueue.query.PlanUserQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface XsPlanUserMapper extends BaseMapper<XsPlanUser> {

    /**
     *
     * @param planUserQuery
     * @return
     */
    List<XsPlanUser> getListByCondition(@Param("row") PlanUserQuery planUserQuery);


}
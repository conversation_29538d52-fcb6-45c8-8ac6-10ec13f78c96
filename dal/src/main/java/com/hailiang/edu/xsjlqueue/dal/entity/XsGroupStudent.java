package com.hailiang.edu.xsjlqueue.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * Table: xs_group_student
 */
@Data
@TableName("xs_group_student")
public class XsGroupStudent {
    /**
     * Column: id
     * Remark: 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * Column: group_id
     * Remark: 组id
     */
    private Long groupId;

    /**
     * Column: student_id
     * Remark: 学生id
     */
    private Long studentId;

    /**
     * Column: sort_val
     * Remark: 排序值
     */
    private Long sortVal;

    /**
     * Column: is_group_leader
     * Remark: 是否组长 0|否 1|是
     */
    private Integer isGroupLeader;

    /**
     * Column: is_small_group_leader
     * Remark: 是否小组长 0|否 1|是
     */
    private Integer isSmallGroupLeader;

    /**
     * Column: create_time
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Remark: 更新时间
     */
    private String updateTime;

    @TableField(exist = false)
    private XsGroup xsGroup;
}
package com.hailiang.edu.xsjlqueue.dal.entity.mongodb;

import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * @Desc:
 * @Data: 2023-05-22 19:29:45
 * @Author: RenJY
 */
@Data
@Document(collection = "awardRecordOther")
public class AwardRecordOther {

    /**  发奖记录id */
    private Long awardRecordId;

    /**  组和学生关系列表 */
    private  List<Group> groupList;

    /**  获奖学生列表 */
    private List<Student> studentList;

    /**
     * 方案下用户id集合
     */
    private List<Long> planUserIds;

    /**  奖状详情 */
    private  List<AwardRecordDetail> details;

    /**  发奖查询积分条件( 搜索查询相关字段) */
    private SearchPointQuery searchPointQuery;

    /**  规则快照 */
    private AwardRule awardRule;

    @Data
    public static class Student {
        /**  学生id */
        private Long studentId;
        /** 学号 */
        private String studentNo;
        /** 学生姓名 */
        private String studentName;
    }

    @Data
    public static class Group {
        /**  学生id or 组id */
        private Long groupId;

        /**  学生名 or 组名称 */
        private String groupName;

        /** 组下学生 */
        private List<Student> studentList;
    }

    @Data
    public static class AwardRecordDetail {
        /** id值 */
        private Long awardRecordDetailId;

        /**
         * 名次称号
         */
        private String titleName;

        /**
         * 名次类型：group|组, personal|个人, progress|进步
         * @see com.hailiang.edu.xsjlqueue.consts.AwardCodeConst
         */
        private String rankCode;

        /**  奖状地址 */
        private String awardUrl;

        /**  名次 */
        private Integer no;

        /**  学生id or 组id */
        private Long businessId;

        /**  学生名 or 组名称 */
        private String businessName;
    }

    @Data
    public static class SearchPointQuery {

        /**
         * saas班级id
         */
        private String saasClassId;

        /**
         * 方案id
         */
        private Long planId;

        /**
         * 开始时间
         */
        private String startTime;

        /**
         * 结束时间
         */
        private String endTime;

        /**
         * 时间区间类型
         */
        private String timeType;

        /**
         * 是否除外 负向积分
         */
        private Boolean isExceptMinusScore;

        /**
         * 除外的渠道类型集合 1|点评项 2|游戏 3|奖品
         */
        private List<Long> exceptChannelIds;

        /**
         * 积分来源老师
         */
        private List<Long> userIds;

        /**
         * 标签ids
         */
        private List<Long> planTagIds;
    }
}

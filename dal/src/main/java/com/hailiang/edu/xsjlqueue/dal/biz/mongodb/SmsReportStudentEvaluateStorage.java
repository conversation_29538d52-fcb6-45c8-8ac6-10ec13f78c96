package com.hailiang.edu.xsjlqueue.dal.biz.mongodb;


import com.hailiang.edu.xsjlqueue.dal.entity.mongodb.SmsReportStudentEvaluate;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Component
public class SmsReportStudentEvaluateStorage {

    @Resource
    private MongoTemplate mongoTemplate;


    public void batchInsert(List<SmsReportStudentEvaluate> smsReportStudentEvaluates) {
        if (CollectionUtils.isEmpty(smsReportStudentEvaluates)) {
            return;
        }
        mongoTemplate.insert(smsReportStudentEvaluates, SmsReportStudentEvaluate.class);
    }


}

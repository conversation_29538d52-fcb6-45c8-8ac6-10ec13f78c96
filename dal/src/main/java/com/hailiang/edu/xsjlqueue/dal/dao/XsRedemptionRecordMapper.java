package com.hailiang.edu.xsjlqueue.dal.dao;


import java.util.Collection;

import com.hailiang.edu.xsjlqueue.dal.entity.XsRedemptionRecord;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <AUTHOR>
 */
public interface XsRedemptionRecordMapper extends BaseMapper<XsRedemptionRecord> {

    int delByStudentIds(@Param("studentIds") Collection<Long> studentIds, @Param("saasClassId") String saasClassId, @Param("updateTime") String updateTime);

}
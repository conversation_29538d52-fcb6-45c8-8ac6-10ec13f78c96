package com.hailiang.edu.xsjlqueue.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Table: xs_sms_report
 */
@Data
@TableName("xs_sms_report")
public class XsSmsReport {
    /**
     * Column: sms_report_id
     * Remark: 报告id
     */
    @TableId(type = IdType.INPUT)
    private Long smsReportId;

    /**
     * Column: user_id
     * Remark: 用户id
     */
    private Long userId;

    /**
     * Column: send_hour
     * Remark: 具体报告发送小时 0-23
     */
    private Integer sendHour;

    /**
     * Column: send_minute
     * Remark: 具体报告发送分钟 0-59
     */
    private Integer sendMinute;

    /**
     * Column: sms_rule_id
     * Remark: 推送规则d
     */
    private Long smsRuleId;

    /**
     * Column: sms_content_range
     * Remark: 推送内容范围 （1|表现明细 2|本期表现 3|行为分析 4|综合发展 逗号隔开存储）
     */
    private String smsContentRange;

    /**
     * Column: start_time
     * Remark: 报告统计开始时间
     */
    private String startTime;

    /**
     * Column: end_time
     * Remark: 报告统计结束时间
     */
    private String endTime;

    /**
     * Column: is_deleted
     * Remark: 是否删除 0|否 1|是
     */
    private Integer isDeleted;

    /**
     * Column: saas_class_id
     * Remark: saas班级id
     */
    private String saasClassId;

    /**
     * Column: saas_school_id
     * Remark: saas学校id
     */
    private String saasSchoolId;

    /**
     * Column: saas_tenant_id
     * Remark: saas租户id
     */
    private String saasTenantId;

    /**
     * Column: create_time
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Remark: 更新时间
     */
    private String updateTime;

    /**
     * Column: sms_rule_meta_data
     * Remark: 推送规则对象json
     */
    private String smsRuleMetaData;
}
package com.hailiang.edu.xsjlqueue.dal.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsStudent;
import com.hailiang.edu.xsjlqueue.query.StudentQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface XsStudentMapper extends BaseMapper<XsStudent> {


    List<XsStudent> getListByCondition(@Param("row") StudentQuery studentQuery);

    int addBatch(@Param("studentList") List<XsStudent> studentList);

    int batchUpdateById(@Param("xsStudentList") List<XsStudent> xsStudentList);

}
package com.hailiang.edu.xsjlqueue.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Table: xs_trust_login
 */
@Data
@TableName("xs_trust_login")
public class XsTrustLogin {
    /**
     * Column: trust_id
     * Remark: 主键
     */
    @TableId(type = IdType.AUTO)
    private Long trustId;

    /**
     * Column: user_id
     * Remark: 用户id
     */
    private Long userId;

    /**
     * Column: open_id
     * Remark: 第三方唯一标识
     */
    private String openId;

    /**
     * Column: platform_code
     * Remark: 第三方平台编码
     */
    private String platformCode;

    /**
     * Column: create_time
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Remark: 更新时间
     */
    private String updateTime;
}
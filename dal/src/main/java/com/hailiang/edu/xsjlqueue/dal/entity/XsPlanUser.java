package com.hailiang.edu.xsjlqueue.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Table: xs_plan_user
 */
@Data
@TableName("xs_plan_user")
public class XsPlanUser {
    /**
     * Column: id
     * Type: INT UNSIGNED
     * Remark: 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * Column: plan_id
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 方案id
     */
    private Long planId;

    /**
     * Column: user_id
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 用户id
     */
    private Long userId;

    /**
     * Column: is_hand_admin
     * Remark: 是否协同管理员 0|否 1|是
     */
    private Integer isHandAdmin;

    /**
     * Column: create_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private String updateTime;


    //关联方案模型
    @TableField(exist = false)
    private XsPlan xsPlan;

}
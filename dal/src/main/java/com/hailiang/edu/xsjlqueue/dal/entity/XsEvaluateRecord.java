package com.hailiang.edu.xsjlqueue.dal.entity;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.hailiang.edu.xsjlqueue.consts.DeletedConst;
import com.hailiang.edu.xsjlqueue.consts.EvaluateStatusConst;
import lombok.Data;

/**
 * Table: xs_evaluate_record
 */
@Data
public class XsEvaluateRecord {
    /**
     * Column: evaluate_record_id
     * Type: BIGINT
     * Default value: 0
     * Remark: 评语记录id
     */
    @TableId(type = IdType.INPUT)
    private Long evaluateRecordId;

    /**
     * Column: user_id
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 创建人id
     */
    private Long userId;

    /**
     * Column: plan_id
     * Type: INT UNSIGNED
     * Default value: 0
     * Remark: 方案id
     */
    private Long planId;

    /**
     * Column: evaluate_name
     * Type: VARCHAR(250)
     * Remark: 评语名称
     */
    private String evaluateName;

    /**
     * Column: evaluate_type
     * Type: VARCHAR(50)
     * Default value: endTerm
     * Remark: 评语类型 期末|endTerm 日常|daily
     */
    private String evaluateType;

    /**
     * Column: start_time
     * Type: TIMESTAMP
     * Remark: 开始日期
     */
    private String startTime;

    /**
     * Column: end_time
     * Type: TIMESTAMP
     * Remark: 结束日期
     */
    private String endTime;

    /**
     * Column: evaluate_status
     * Type: VARCHAR(50)
     * Default value: unSave
     * Remark: 评语记录状态 unSave｜未保存 save｜已保存 temSave|暂时保存
     * @see EvaluateStatusConst
     */
    private String evaluateStatus;

    /**
     * @see com.hailiang.edu.xsjlqueue.enums.RoleEnum
     */
    private String roleCode;

    /**
     * Column: user_role_name
     * Type: VARCHAR(250)
     * Remark: 评价身份
     */
    private String userRoleName;

    /**
     * Column: is_deleted
     * Type: BIT
     * Default value: 0
     * Remark: 是否删除 0|否 1|是
     */
    private Integer isDeleted;

    /**
     * Column: saas_class_id
     * Type: VARCHAR(100)
     * Remark: saas班级id
     */
    private String saasClassId;

    /**
     * Column: saas_school_id
     * Type: VARCHAR(100)
     * Remark: saas学校id
     */
    private String saasSchoolId;

    /**
     * Column: saas_tenant_id
     * Type: VARCHAR(100)
     * Remark: saas租户id
     */
    private String saasTenantId;

    /**
     * Column: create_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Type: TIMESTAMP
     * Default value: CURRENT_TIMESTAMP
     * Remark: 更新时间
     */
    private String updateTime;

    public void init() {
        this.createTime = DateUtil.now();
        this.updateTime = DateUtil.now();
        this.isDeleted = DeletedConst.NO;
    }
}
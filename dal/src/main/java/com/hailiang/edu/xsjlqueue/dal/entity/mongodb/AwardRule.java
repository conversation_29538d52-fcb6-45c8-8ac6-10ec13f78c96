package com.hailiang.edu.xsjlqueue.dal.entity.mongodb;

import cn.hutool.core.date.DateUtil;
import com.hailiang.edu.xsjlqueue.consts.DeletedConst;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * @Desc:
 * @Data: 2023-05-22 19:28:58
 * @Author: RenJY
 */
@Data
@Document(collection = "awardRule")
public class AwardRule {

    /** 主键  */
    private Long awardRuleId;

    /** 计划id */
    private Long planId;

    /** 创建人 */
    private Long userId;

    /** 规则类型：system/custom */
    private String type;

    /** 奖状称呼 */
    private String callName;

    /** 奖状内容 */
    private String content;

    /** 颁奖机构 */
    private String awardOrgan;

    /** 奖状称号生成方式：system/custom */
    private String titleMethod;

    /** 是否显示班级名 */
    private Boolean isShowClassName;

    /** 选择的图片id */
    private Long sysIconId;

    /** 颁奖名次 */
    private List<AwardRankDto> awardRankList;

    @Data
    public static class AwardRankDto {

        /**
         * 名次 code 类型  group|组, personal|个人, progress|进步
         * @see com.hailiang.edu.xsjlqueue.consts.AwardCodeConst
         */
        private String rankCode;

        /** 前几名 */
        private Integer topNum;

    }


    /** 奖状称号 */
    private List<AwardTitleDto> titleList;

    @Data
    public static class AwardTitleDto{

        /**
         * 称号 code 类型  group|组, personal|个人, progress|进步
         * @see com.hailiang.edu.xsjlqueue.consts.AwardCodeConst
         */
        private String titleCode;

        /** 名次,如第一名，第二名，第三名 */
        private Integer no;

        /** 名称 */
        private String name;
    }

    /** saas班级id */
    private Long saasClassId;

    /** saas学校id */
    private Long saasSchoolId;

    /** saas校区id */
    private Long saasCampusId;

    /** saas年级code */
    private String saasGradeCode;

    /** 创建时间 */
    private String createTime;

    //秒时间戳
    private Long createTimeStamp;

    /** 更新时间 */
    private String updateTime;

    //秒时间戳
    private Long updateTimeStamp;

    private Integer isDeleted;


    public void init(){

        String currentTime = DateUtil.now();
        long seconds = DateUtil.currentSeconds();
        this.setCreateTime(currentTime);
        this.setCreateTimeStamp(seconds);

        this.setUpdateTime(currentTime);
        this.setUpdateTimeStamp(seconds);

        this.setIsDeleted(DeletedConst.NO);

    }


}

package com.hailiang.edu.xsjlqueue.dal.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsAwardRecord;
import com.hailiang.edu.xsjlqueue.query.AwardRecordQuery;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface XsAwardRecordMapper extends BaseMapper<XsAwardRecord> {

    List<XsAwardRecord> getListByCondition(@Param("row") AwardRecordQuery awardRecordQuery);
}
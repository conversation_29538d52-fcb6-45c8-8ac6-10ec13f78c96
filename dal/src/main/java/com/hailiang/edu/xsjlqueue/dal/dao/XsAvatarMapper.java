package com.hailiang.edu.xsjlqueue.dal.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsAvatar;
import com.hailiang.edu.xsjlqueue.query.XsAvatarQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface XsAvatarMapper extends BaseMapper<XsAvatar> {

    /**
     * 批量新增
     * @param xsAvatarList
     * @return
     */
    int insertBatch(@Param("xsAvatarList") List<XsAvatar> xsAvatarList);

    /**
     * 根据条件查询头像信息
     * @param xsFileQuery
     * @return
     */
    List<XsAvatar> getListByCondition(@Param("row") XsAvatarQuery xsFileQuery);


}
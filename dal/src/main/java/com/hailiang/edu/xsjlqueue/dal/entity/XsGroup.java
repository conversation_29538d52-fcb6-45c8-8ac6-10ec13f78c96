package com.hailiang.edu.xsjlqueue.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * Table: xs_group
 */
@Data
@TableName("xs_group")
public class XsGroup {
    /**
     * Column: group_id
     * Remark: 分组id
     */
    @TableId(type = IdType.AUTO)
    private Long groupId;

    /**
     * Column: group_name
     * Remark: 组名称
     */
    private String groupName;

    /**
     * Column: icon
     * Remark: 图标
     */
    private String icon;

    /**
     * Column: plan_id
     * Remark: 方案id
     */
    private Long planId;

    /**
     * Column: user_id
     * Remark: 用户id
     */
    private Long userId;

    /**
     * Column: saas_class_id
     * Remark: saas班级id
     */
    private String saasClassId;

    /**
     * Column: saas_school_id
     * Remark: saas学校id
     */
    private String saasSchoolId;

    /**
     * Column: saas_tenant_id
     * Remark: saas租户id
     */
    private String saasTenantId;

    /**
     * Column: is_deleted
     * Remark: 是否删除 0|否 1|是
     */
    private Integer isDeleted;

    /**
     * Column: create_time
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Remark: 更新时间
     */
    private String updateTime;
}
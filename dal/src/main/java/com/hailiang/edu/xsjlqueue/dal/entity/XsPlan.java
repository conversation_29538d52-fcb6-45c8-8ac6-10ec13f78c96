package com.hailiang.edu.xsjlqueue.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Table: xs_plan
 */
@Data
@TableName("xs_plan")
public class XsPlan {
    /**
     * Column: plan_id
     * Remark: 方案id
     */
    @TableId(type = IdType.AUTO)
    private Long planId;

    /**
     * Column: user_id
     * Remark: 用户id
     */
    private Long userId;

    /**
     * Column: plan_name
     * Remark: 方案名称
     */
    private String planName;

    /**
     * Column: point_retail_rate
     * Remark: 积分比例
     */
    private Double pointRetailRate;

    /**
     * Column: plan_type
     * Remark: 方案类型 1|常规分组 2|不分组 3|师徒帮扶分组
     */
    private Integer planType;

    /**
     * Column: group_type
     * Remark: 分组类型 manual|手动 auto|自动
     */
    private String groupType;

    /**
     * Column: group_num
     * Remark: 几人组 4,6,8
     */
    private Integer groupNum;

    /**
     * Column: is_used
     * Remark: 是否使用 0|否 1|是
     */
    private Integer isUsed;

    /**
     * Column: saas_class_id
     * Remark: saas班级id
     */
    private String saasClassId;

    /**
     * Column: saas_class_name
     * Remark: saas班级名称
     */
    private String saasClassName;

    /**
     * Column: saas_class_alias
     * Remark: saas班级别名
     */
    private String saasClassAlias;

    /**
     * Column: saas_school_id
     * Remark: saas学校id
     */
    private String saasSchoolId;

    /**
     * Column: saas_school_name
     * Remark: saas学校名称
     */
    private String saasSchoolName;

    /**
     * Column: saas_tenant_id
     * Remark: saas租户id
     */
    private String saasTenantId;

    /**
     * Column: is_deleted
     * Remark: 是否删除 0|否 1|是
     */
    private Integer isDeleted;

    /**
     * Column: create_time
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Remark: 更新时间
     */
    private String updateTime;
}
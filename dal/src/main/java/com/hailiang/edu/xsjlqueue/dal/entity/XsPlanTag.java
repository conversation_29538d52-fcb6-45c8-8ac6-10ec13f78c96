package com.hailiang.edu.xsjlqueue.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Table: xs_plan_tag
 */
@Data
@TableName("xs_plan_tag")
public class XsPlanTag {
    /**
     * Column: plan_tag_id
     * Remark: 主键
     * 使用雪花算法塞id
     */
    @TableId(type = IdType.INPUT)
    private Long planTagId;

    /**
     * Column: plan_tag_name
     * Remark: 标签名称
     */
    private String planTagName;

    /**
     * Column: plan_id
     * Remark: 方案id
     */
    private Long planId;

    /**
     * Column: user_id
     * Remark: 用户id
     */
    private Long userId;

    /**
     * Column: sort_val
     * Remark: 排序值
     */
    private Long sortVal;

    /**
     * Column: saas_class_id
     * Remark: saas班级id
     */
    private String saasClassId;

    /**
     * Column: saas_school_id
     * Remark: saas学校id
     */
    private String saasSchoolId;

    /**
     * Column: saas_tenant_id
     * Remark: saas租户id
     */
    private String saasTenantId;

    /**
     * Column: is_deleted
     * Remark: 是否删除 0|否 1|是
     */
    private Integer isDeleted;

    /**
     * Column: create_time
     * Remark: 创建时间
     */
    private String createTime;

    /**
     * Column: update_time
     * Remark: 更新时间
     */
    private String updateTime;

    /**
     * Column: comment_template_id
     * Remark: 点评项模版id
     */
    private Long commentTemplateId;

    /**
     * Column: apply_level
     * Remark: 应用级别 general|普通 school|校级
     */
    private String applyLevel;

    /**
     * Column: module_code
     * Remark: 综评 0|无 1|德 2|智 3|体 4|美 5|劳
     */
    private Integer moduleCode;
}
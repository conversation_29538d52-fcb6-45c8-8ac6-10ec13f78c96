package com.hailiang.edu.xsjlqueue.dal.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsSmsReportStudent;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface XsSmsReportStudentMapper extends BaseMapper<XsSmsReportStudent> {

    void batchAdd(@Param("list") List<XsSmsReportStudent> xsSmsReportStudentList);

    int getOtherReportCount(@Param("studentIds") Set<Long> studentIds, @Param("smsReportId") Long smsReportId);

    List<Long> getOtherReportStudent(@Param("studentIds") Set<Long> studentIds, @Param("smsReportId") Long smsReportId);
}
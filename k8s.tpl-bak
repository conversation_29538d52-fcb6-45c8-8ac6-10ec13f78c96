---
apiVersion: v1
kind: Service
metadata:
  name: {{APP_NAME}}
  namespace: {{NAMESPACE}}
spec:
  type: ClusterIP
  ports:
  - port: {{PORT}}
    protocol: TCP
    targetPort: {{PORT}}
  selector:
    app: {{APP_NAME}}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{APP_NAME}}
  namespace: {{NAMESPACE}}
spec:
  selector:
    matchLabels:
      app: {{APP_NAME}}
  replicas: {{NUM}}
  template:
    metadata:
      labels:
        app: {{APP_NAME}}
    spec:
      containers:
        - name: {{APP_NAME}}
          env:
            - name: TZ
              value: Asia/Shanghai
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: POD_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: NACOS_SERVER_ADDR
              valueFrom:
                configMapKeyRef:
                  name: nacos-info
                  key: NACOS_SERVER_ADDR
            - name: NACOS_NAMESPACE                               
              valueFrom:                                      
                configMapKeyRef:                             
                  name: nacos-info
                  key: NACOS_NAMESPACE
          image: {{IMAGE_URL}}:{{IMAGE_TAG}}
          imagePullPolicy: IfNotPresent
          resources:
            requests:
              memory: 1024Mi
              cpu: 1
            limits:
              memory: 1536Mi
              cpu: 2
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
          readinessProbe:
            tcpSocket:
              port: {{PORT}}
            initialDelaySeconds: 10
            timeoutSeconds: 5
            periodSeconds: 60
            successThreshold: 1
            failureThreshold: 3
          ports:
            - containerPort: {{PORT}}
              name: port
              protocol: TCP
          command: ["/bin/sh"]
          args: ["-c", "set -e && java {{jvm_options}} -jar  {{APP_JAR}} "]
          lifecycle:
            preStop:
              exec:
                command: ['/bin/sh','-c','/app/nacosDown.sh']
      terminationGracePeriodSeconds: 120
      volumes:
        - name: host-time
          hostPath:
            path: /etc/localtime
            type: ''
      imagePullSecrets:
      - name: harbor-secret
---
apiVersion: v1
data:
  .dockerconfigjson: {{DOCKER_SC}}
kind: Secret
metadata:
  name: harbor-secret
  namespace: {{NAMESPACE}}
type: kubernetes.io/dockerconfigjson

package com.hailiang.edu.xsjlqueue.dto;


import lombok.Data;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;


/**
 * 统一日志格式类
 */
@Data
public class LogFormat {
    /**
     * 请求唯一id
     */
    private String reqId;

    /**
     * 服务名称
     */
    private String serverName;

    /**
     * 记录行为
     */
    private String op;
    /**
     * 记录来源文件
     */
    private String file;
    /**
     * 记录来源文件代码所在行数
     */
    private String fileLine;
    /**
     * 记录异常抛出
     */
    private String msg;


    public String format()
    {
        try{
            StringBuilder stringBuilder = new StringBuilder();
            String chars = "||";

            Class<?> text = this.getClass();
            Field[] fields = text.getDeclaredFields();

            for (Field field:fields)
            {
                // 为了兼容反射私有属性
                field.setAccessible(true);
                boolean isStatic = Modifier.isStatic(field.getModifiers());
                if(!isStatic && null != field.get(this)){
                    stringBuilder.append(field.getName()).append(":")
                            .append(field.get(this)).append(chars);
                }
            }
            String str = stringBuilder.toString();
            if(!"".equals(str)){
                str = str.substring(0,str.length() - chars.length());
            }

            return str;
        }catch (Exception e)
        {
            return "";
        }
    }
}


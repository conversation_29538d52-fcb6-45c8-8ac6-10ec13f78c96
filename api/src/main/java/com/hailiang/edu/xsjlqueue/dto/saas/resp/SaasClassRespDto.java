package com.hailiang.edu.xsjlqueue.dto.saas.resp;

import lombok.Data;

/**
 * <AUTHOR>
 * @version v0.1: SaasSchoolClassRespDto.java, v 0.1 2023年09月19日 18:00  zhousx Exp $
 */
@Data
public class SaasClassRespDto {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 年级id
     */
    private Long gradeId;

    /**
     * 学校id
     */
    private Long schoolId;

    /**
     * 所属校区
     */
    private Long campusId;

    /**
     * 老师的id
     */
    private Long staffId;

    /**
     * 原始的班级编码id
     */
    private Long regionClassId;

    /**
     * 班级别名
     */
    private String classAlias;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 是否显示别名
     */
    private String isAlias;

    /**
     * 班级类型：0行政班、1选考班、2学考班、3选修班、4兴趣班
     */
    private String classType;

    /**
     * 班级状态：0正常、1已结课
     */
    private String classStatus;

    /**
     * 学生人数
     */
    private Integer studentNum;

    /**
     * 班级流水号
     */
    private Integer classNum;

    /**
     * 升级状态:0正常1已毕业
     */
    private String upgradeStatus;

    /**
     * 年级code
     */
    private String gradeCode;

    /**
     * 年级name
     */
    private String gradeName;

    /**
     * 入学年份
     */
    private Integer startYear;

}

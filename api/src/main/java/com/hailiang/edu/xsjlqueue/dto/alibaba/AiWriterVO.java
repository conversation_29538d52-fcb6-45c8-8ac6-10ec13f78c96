package com.hailiang.edu.xsjlqueue.dto.alibaba;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiWriterVO {
    /**
     * 系统生成的标志本次调用的id
     */
    @JsonProperty("request_id")
    private String requestId;

    /**
     * 模型答复相关信息
     */
    @JsonProperty("output")
    private AiWriterOutputVO aiWriterOutputVO;

}

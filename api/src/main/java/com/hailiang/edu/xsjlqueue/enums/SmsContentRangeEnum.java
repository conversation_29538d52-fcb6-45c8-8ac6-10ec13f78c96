package com.hailiang.edu.xsjlqueue.enums;


import lombok.Getter;

@SuppressWarnings("all")
@Getter
public enum SmsContentRangeEnum {
//（1|表现明细 2|本期表现 3|行为分析 4|综合发展 5｜奖状 逗号隔开存储）
    POINT("1","表现明细"),
    CURRENT_POINT("2","本期表现"),
    BEHAVIOR("3","行为分析"),
    DEVELOP("4","综合发展"),
    AWARD("5","奖状"),
    EVALUATE("6", "评语"),
    ;

    SmsContentRangeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private final String code;
    private final String name;
}

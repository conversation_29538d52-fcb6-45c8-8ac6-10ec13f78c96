package com.hailiang.edu.xsjlqueue.dto.saas.resp;

import lombok.Data;

import java.util.List;

@Data
public class TeachManageStaffTeachRespDto {


    private List<LeaderClassInfo> leaderClassInfos;

    @Data
    public static class LeaderClassInfo {

        private Long classId;

        private String className;

    }

    private List<LeaderGradeInfo> leaderGradeInfos;


    @Data
    public static class LeaderGradeInfo {

        private Long gradeId;

        private String gradeName;

    }


    private Long staffId;


    private List<TeachClassInfo> teachClassInfos;

    @Data
    public static class TeachClassInfo {

        private Long classId;

        private String className;

        private String classType;

        private String sectionName;

        private List<Subject> subjects;

        @Data
        public static class Subject {

            private Long subjectId;

            private String subjectName;

        }

    }
}

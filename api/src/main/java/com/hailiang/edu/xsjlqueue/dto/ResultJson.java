package com.hailiang.edu.xsjlqueue.dto;



import com.alibaba.fastjson.annotation.JSONField;
import com.hailiang.edu.xsjlqueue.enums.ApiCodeEnum;
import lombok.Data;

/**
 * 接口通用json响应返回结构
 *
 * <AUTHOR>
 */
@Data
public class ResultJson {

    @JSONField(ordinal = 1)
    private int code;

    @JSONField(ordinal = 2)
    private String msg;

    @JSONField(ordinal = 3)
    private Object data;


    public ResultJson(int code , String msg)
    {
        this.code = code;
        this.msg = null != msg ? msg:"";
        this.data = new Object();
    }

    public ResultJson(int code , String msg, Object data)
    {
        this.code = code;
        this.msg = null != msg ? msg:"";
        this.data = null != data ? data:new Object();
    }

    public static ResultJson success(Object data) {
        ResultJson result = new ResultJson(ApiCodeEnum.SUCCESS.getCode(), ApiCodeEnum.SUCCESS.getMsg());
        result.setData(data);
        return result;
    }


    public static ResultJson fail(Exception e) {
        return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), e.getMessage());
    }
}


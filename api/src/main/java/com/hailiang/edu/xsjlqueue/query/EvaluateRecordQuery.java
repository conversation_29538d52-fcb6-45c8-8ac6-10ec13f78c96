package com.hailiang.edu.xsjlqueue.query;

import lombok.Data;

import java.util.Collection;


@Data
public class EvaluateRecordQuery {

    private String evaluateRecordId;

    private String saasClassId;

    private Long planId;

    private String startTime;

    private String endTime;

    private Boolean filterUnSave;

    private String evaluateStatus;

    private Collection<Long> planIds;

    private Collection<Long> userIds;

    private String roleCode;
}

package com.hailiang.edu.xsjlqueue.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;


@Configuration
@Data
public class SlsConfig {

    @Value("${sls.accessId}")
    private String accessId;


    @Value("${sls.accessKey}")
    private String accessKey ;

    @Value("${sls.host}")
    private String host;

    @Value("${sls.projectName}")
    private String projectName;

    @Value("${sls.logstoreName}")
    private String logstoreName;
}

package com.hailiang.edu.xsjlqueue.dto.saas.resp;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @version v0.1: SaasSchoolRespDto.java, v 0.1 2023年09月19日 21:00  zhousx Exp $
 */
@Data
public class SaasSchoolRespDto {

    private Long id;

    private Long tenantId;

    private String tenantName;

    private String schoolName;

    private Long orgId;

    private Boolean isCharge;

    private List<SimpleRoleDto> roleList;

    @Data
    public static class SimpleRoleDto {

        private String roleId;

        private String roleName;

        private String roleCode;
    }

    private List<SimpleGradeDto> gradeList;

    @Data
    public static class SimpleGradeDto {

        private String gradeId;

        private String gradeCode;

        private String gradeName;

        private String sectionCode;

        private String sectionName;
    }


    private List<SimpleSubjectDto> subjectList;

    @Data
    public static class SimpleSubjectDto {

        private String subjectId;

        private String subjectCode;

        private String subjectName;

        private List<SimpleSectionDto> sectionList;
    }

    @Data
    public static class SimpleSectionDto {

        private String sectionCode;

        private String sectionName;
    }


    private List<SimpleGradeDto> schoolGradeList;

    private List<SimpleSubjectDto> schoolSubjectList;

    private String provinceCode;

    private String cityCode;

    private String areaCode;

    private String provinceName;

    private String cityName;

    private String areaName;


}

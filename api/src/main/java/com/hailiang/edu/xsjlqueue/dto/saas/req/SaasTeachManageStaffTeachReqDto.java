package com.hailiang.edu.xsjlqueue.dto.saas.req;


import lombok.Data;

import java.util.List;

@Data
public class SaasTeachManageStaffTeachReqDto {

    /**
     * 员工id集合
     */
    private List<Long> staffIds;

    /**
     * 班级类型：0行政班、1选考班、2学考班、3选修班、4兴趣班  不传或者传的是空集合默认查所有
     */
    private List<String> classTypes;

    /**
     * 毕业状态（0未毕业1已毕业），不传查所有
     */
    private String graduationStatus;

    /**
     * 学校id
     */
    private Long schoolId;

}

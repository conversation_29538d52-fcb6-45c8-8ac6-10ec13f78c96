package com.hailiang.edu.xsjlqueue.dto.saas.resp;

import lombok.Data;

@Data
public class UnderClassInfoRespDto {

    private Long campusId;

    private String campusName;

    private Long campusSectionId;

    private String classAlias;

    private String classDivisionsCode;

    private String classDivisionsValue;

    private Long classLevelId;

    private String classLevelName;

    private String className;

    private Long classNum;

    private String classStatus;

    private String classType;

    private Long gradeId;

    private String gradeName;

    private Long id;

    private String isAlias;

    private Long regionClassId;

    private Long schoolId;

    private String schoolName;

    private String sectionCode;

    private Long staffId;

    private Long studentNum;

    private Long tenantId;

    private String tenantName;

    private String upgradeStatus;

}

package com.hailiang.edu.xsjlqueue.dto.stu;

import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;

/**
 * 学生数据比对DTO
 * <AUTHOR>
 * @version v0.1: StudentDataSyncTaskDto.java, v 0.1 2023年09月19日 17:14  zhousx Exp $
 */
@Data
public class StudentDataSyncTaskDto {

    /**
     * 任务id 雪花算法
     */
    @JSONField(ordinal = 1)
    private Long       stuSyncTaskId;

    /**
     * 班级ID集合
     */
    @JSONField(ordinal = 2)
    private List<Long> classIds;

    /**
     * 本批消息条数
     */
    @JSONField(ordinal = 3)
    private Integer msgCount = 0;

    /**
     * 同一批数据标识ID
     */
    @JSONField(ordinal = 4)
    private Long syncJobId;

}

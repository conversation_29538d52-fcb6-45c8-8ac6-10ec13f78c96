package com.hailiang.edu.xsjlqueue.dto.saas.req;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @version v0.1: SaasSchoolClassQueryReqDto.java, v 0.1 2023年09月19日 20:48  zhousx Exp $
 */
@Data
public class SaasSchoolClassQueryReqDto {

    /**
     * 班级id集合
     */
    private List<Long> classIds;
    /**
     * 班级类型：0行政班、1选考班、2学考班、3选修班、4兴趣班  不传或者传的是空集合默认查所有
     */
    private List<String> classTypes;
    /**
     * 年级id
     */
    private Long gradeId;
    /**
     * 毕业状态（0未毕业1已毕业），不传查所有
     */
    private String graduationStatus;
    /**
     * 学校id【必填】
     */
    private Long schoolId;
}

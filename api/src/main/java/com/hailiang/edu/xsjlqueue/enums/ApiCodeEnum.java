package com.hailiang.edu.xsjlqueue.enums;


import com.hailiang.edu.xsjlqueue.consts.ApiCodeConst;

/**
 *
 * <AUTHOR>
 */
public enum ApiCodeEnum {

    //正常业务处理
    SUCCESS(ApiCodeConst.CODE_SUCCESS,"success"),
    //一般类通用异常业务处理
    NORMAL_ERROR(ApiCodeConst.CODE_ERROR,"fail"),

    //未登录异常
    LOGIN_ERROR(ApiCodeConst.CODE_LOGIN_ERROR,"unLogin"),
    ;

    int code;
    String msg;
    Object data;

    ApiCodeEnum(int code, String msg)
    {
        this.code = code;
        this.msg = msg;
        this.data = new Object();
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}



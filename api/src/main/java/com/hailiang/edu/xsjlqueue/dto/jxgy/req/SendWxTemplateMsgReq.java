package com.hailiang.edu.xsjlqueue.dto.jxgy.req;

import lombok.Data;

import java.util.List;

@Data
public class SendWxTemplateMsgReq {

    /**
     * 消息类型：
     * internalDrive:学生内驱力推送家长
     */
    private String type;

    /**
     * 模板消息-标题
     */
    private String first;

    /**
     * 模板消息-班级
     */
    private String keyword1;

    /**
     * 模板消息-通知人
     */
    private String keyword2;

    /**
     * 模板消息-时间
     */
    private String keyword3;

    /**
     * 模板消息-备注
     */
    private String remark;

    /**
     * 跳转地址
     */
    private String url;

    /**
     * 跳转类型，0：跳小程序，1：跳h5
     */
    private String urlType;

    /**
     * 发送的学生列表
     */
    private List<StudentDto> studentList;

    @Data
    public static class StudentDto{

        /**
         * 学生学号
         */
        private String studentCode;

        /**
         * 学生姓名
         */
        private String studentName;

        /**
         * saas学生id
         */
        private String saasStudentId;

        /**
         * 跳转地址参数
         */
        private String urlParam;

        /**
         * 模板消息-通知内容
         */
        private String keyword4;
    }




    private String appid;

    private String timestamp;

    private String nonceStr;

    private String sign;
}

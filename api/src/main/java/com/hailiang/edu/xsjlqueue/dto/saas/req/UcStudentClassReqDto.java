/**
 * Hailiang.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.hailiang.edu.xsjlqueue.dto.saas.req;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version v0.1: UcStudentClassReqDto.java, v 0.1 2023年09月19日 19:48  zhousx Exp $
 */
@Data
public class UcStudentClassReqDto {

    private Long studentId;

    @NotNull(message = "学校id必填")
    private Long schoolId;

    private Long campusId;

    private Long gradeId;

    private Long classId;

    private String studentStatus;

    private String upgradeStatus;

    private Long campusSectionId;

    private Integer pageNum = 1;

    @Max(value = 5000, message = "pageSize不能超过限制条数5000")
    private Integer pageSize = 20;

}
package com.hailiang.edu.xsjlqueue.util;



/**
 *
 * 雪花算法 获取分布式id
 * <AUTHOR>
 */
public class SnowFlakeUtil {

    public SnowFlakeUtil()
    {
    }
    public SnowFlakeUtil(long machineId){
        if(machineId > 32) {
            throw new RuntimeException("机器位长");
        }
        this.machineId = machineId;
    }


    public SnowFlakeUtil(long machineId, long dataCenterId){
        if(machineId > 32) {
            throw new RuntimeException("机器位长");
        }
        this.machineId = machineId;
        this.dataCenterId = dataCenterId;
    }


    /**
     * 起始时间戳
     */
    private final static long START_STMP = 1480166465631L;

    /**
     * 每一部分占用的位数
     */
    private final static long SEQUENCE_BIT = 12;//序列号占用的位数
    private final static long MACHINE_BIT = 5;//机器标识占用的位数 最后32个机器
    private final static long DATACENTER_BIT = 5;//数据中心占用的位数


    /**
     * 每一部分的最大值
     */
    private final static long MAX_DATACENTER_NUM = -1L^ (-1L << DATACENTER_BIT);
    private final static long MAX_MACHINE_NUM = -1L^ (-1L << MACHINE_BIT);
    private final static long MAX_SEQUENCE = -1L^ (-1L << SEQUENCE_BIT);

    /**
     * 每一部分向左的位移
     */
    private final static long MACHINE_LEFT = SEQUENCE_BIT + DATACENTER_BIT;
    private final static long TIMESTMP_LEFT = MACHINE_LEFT + MACHINE_BIT;

    /**
     * 机器标识
     */
    private long machineId = 0L;

    /**
     * 数据中心标识
     */
    private long dataCenterId = 0L;

    /**
     * 序列号
     */
    private static long sequence = 0L;

    /**
     * 上一次时间戳
     */
    private static long lastStmp = -1L;

    /**
     * 产生下一个ID
     * @return
     */
    public synchronized long nextId()
    {
        long currStmp = getNewstmp();
        if(currStmp < lastStmp) {
            throw new RuntimeException("Clock moved backwards Refusing to generate id");
        }

        if(currStmp == lastStmp) {
            sequence = (sequence +1 )& MAX_SEQUENCE;
            if(sequence == 0L){
                currStmp = getNextMill();
            }
        }else {
            sequence = 0L;
        }
        lastStmp = currStmp;

        return (currStmp - START_STMP) << TIMESTMP_LEFT //时间戳部分
                | machineId << MACHINE_LEFT // 机器部分
                | dataCenterId << DATACENTER_BIT //数据中心部分
                | sequence;
    }


    private long getNextMill()
    {
        long mill = getNewstmp();
        while (mill < lastStmp){
            mill = getNewstmp();
        }
        return mill;
    }

    private long getNewstmp()
    {
        return System.currentTimeMillis();
    }

/*    public static void main(String[] args) {
        SnowFlake snowFlake = new SnowFlake();
        System.out.println(snowFlake.nextId());
    }*/
}



package com.hailiang.edu.xsjlqueue.util;



import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 *
 * 集合操作类
 * <AUTHOR>
 */
@Component
public class SetUtil<T> {

    /**
     * 交集 例如 集合1 为 [2,3,4] 集合2 为 [3,11,15] 返回结果为 [3]
     * @param set1
     * @param set2
     * @return
     */
    public Set<T> intersection(Set<T> set1,Set<T> set2)
    {
        Set<T> result = new HashSet<T>();
        result.addAll(set1);
        result.contains(set2);
        return result;
    }

    /**
     * 并集 例如 集合1 为 [2,3,4] 集合2 为 [3,11,15] 返回结果为 [2,3,4,11,15]
     * @param set1
     * @param set2
     * @return
     */
    public Set<T> union(Set<T> set1,Set<T> set2)
    {
        Set<T> result = new HashSet<T>();
        result.addAll(set1);
        result.addAll(set2);
        return result;
    }

    /**
     * 差集 例如 集合1 为 [2,3,4] 集合2 为 [3,11,15] 返回结果为 [2,4]
     * @param set1
     * @param set2
     * @return
     */
    public Set<T> difference(Set<T> set1,Set<T> set2)
    {
        Set<T> result = new HashSet<T>();
        result.addAll(set1);
        result.removeAll(set2);
        return result;
    }

}






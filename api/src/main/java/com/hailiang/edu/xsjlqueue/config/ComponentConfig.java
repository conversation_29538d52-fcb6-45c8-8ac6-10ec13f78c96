package com.hailiang.edu.xsjlqueue.config;


import cn.hutool.core.date.DateUtil;
import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.aliyun.openservices.log.Client;
import com.hailiang.edu.xsjlqueue.util.RedisUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.util.Map;


@Configuration
public class ComponentConfig {

    /**
     * redis 第一个实例
     */
    @Resource(name = "stringRedisTemplate")
    StringRedisTemplate stringRedisTemplate;

    @Bean
    RedisUtil redisUtil() {
        return new RedisUtil(stringRedisTemplate);
    }

    /**
     * redis 第二个实例
     */
    @Resource(name = "stringRedisTemplate2")
    StringRedisTemplate stringRedisTemplate2;


    @Bean("redisUtil2")
    RedisUtil redisUtil2() {
        return new RedisUtil(stringRedisTemplate2);
    }

    @Resource
    SlsConfig slsConfig;


    @Bean
    Client client() {
        return new Client(slsConfig.getHost(), slsConfig.getAccessId(), slsConfig.getAccessKey());
    }


    @Bean
    public NacosDiscoveryProperties nacosProperties() {
        NacosDiscoveryProperties nacosDiscoveryProperties = new NacosDiscoveryProperties();
        Map<String, String> metadata = nacosDiscoveryProperties.getMetadata();
        metadata.put("startup.time", DateUtil.now());
        return nacosDiscoveryProperties;
    }
}




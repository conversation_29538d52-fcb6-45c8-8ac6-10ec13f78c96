package com.hailiang.edu.xsjlqueue.dto.saas.req;

import lombok.Data;

/**
 * <AUTHOR>
 * @version v0.1: SaasClassStudentPageQueryReqDto.java, v 0.1 2023年09月19日 21:48  zhousx Exp $
 */
@Data
public class SaasClassStudentPageQueryReqDto {
    /**
     * 第几页【必填】
     */
    private Integer pageNum = 1;
    /**
     * 每页数据条数，默认20（限制条数5000)【必填】
     */
    private Integer pageSize = 20;

    /**
     * 学校id【必填】
     */
    private Long schoolId;
    /**
     * 校区id
     */
    private Long campusId;
    /**
     * 学段id
     */
    private Long campusSectionId;
    /**
     * 年级id
     */
    private Long gradeId;
    /**
     * 班级id
     */
    private Long classId;
    /**
     * 学生信息id
     */
    private Long studentId;
    /**
     * 学生状态0正常1休学
     */
    private String studentStatus;
    /**
     * 升级状态:0正常1已毕业
     */
    private String upgradeStatus;
}

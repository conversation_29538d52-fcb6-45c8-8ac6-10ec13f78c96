package com.hailiang.edu.xsjlqueue.util;


import com.hailiang.edu.xsjlqueue.dto.LogFormat;
import com.hailiang.hr.common.hr.threadCache.ReqThreadCache;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.UUID;


@Component
@Log4j2
public class LogFormatUtil {

    @Value("${spring.application.name}")
    private String serverName;

    public void formatInfo(String msg) {
        int index = 0;
        int length = Thread.currentThread().getStackTrace().length;
        if (length > 2) {
            //调试出堆栈都是列表索引2的位置为调用此方法位置
            index = 2;
        }
        StackTraceElement stackTraceElement = Thread.currentThread().getStackTrace()[index];
        LogFormat logFormat = new LogFormat();

        if (ReqThreadCache.getReqId() == null) {
            ReqThreadCache.setReqId(Md5Util.stringToMD5(UUID.randomUUID().toString()));
        }

        logFormat.setReqId(ReqThreadCache.getReqId());
        logFormat.setOp("info");
        logFormat.setServerName(serverName);
        logFormat.setFile(stackTraceElement.getClassName());
        logFormat.setFileLine(String.valueOf(stackTraceElement.getLineNumber()));
        logFormat.setMsg(msg);
        log.info(logFormat.format());
    }

    public void exceptionPrint(Exception e) {
        StringWriter sw = new StringWriter();
        e.printStackTrace(new PrintWriter(sw, true));
        log.info("e:{}", sw.toString());
    }


    public void formatError(String msg) {
        int index = 0;
        int length = Thread.currentThread().getStackTrace().length;
        if (length > 2) {
            //调试出堆栈都是列表索引2的位置为调用此方法位置
            index = 2;
        }
        StackTraceElement stackTraceElement = Thread.currentThread().getStackTrace()[index];
        LogFormat logFormat = new LogFormat();

        if (ReqThreadCache.getReqId() == null) {
            ReqThreadCache.setReqId(Md5Util.stringToMD5(UUID.randomUUID().toString()));
        }

        logFormat.setReqId(ReqThreadCache.getReqId());
        logFormat.setOp("error");
        logFormat.setServerName(serverName);
        logFormat.setFile(stackTraceElement.getClassName());
        logFormat.setFileLine(String.valueOf(stackTraceElement.getLineNumber()));
        logFormat.setMsg(msg);
        log.error(logFormat.format());
    }
}




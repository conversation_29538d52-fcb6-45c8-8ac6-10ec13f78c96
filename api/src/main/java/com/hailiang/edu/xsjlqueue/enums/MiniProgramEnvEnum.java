package com.hailiang.edu.xsjlqueue.enums;


import lombok.Getter;

@SuppressWarnings("all")
@Getter
public enum MiniProgramEnvEnum {
    //小程序环保变量参数
    DEVELOP("develop","开发"),
    TRIAL("trial","测试"),
    RELEASE("release","生产"),
    ;

    MiniProgramEnvEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private final String code;
    private final String name;
}

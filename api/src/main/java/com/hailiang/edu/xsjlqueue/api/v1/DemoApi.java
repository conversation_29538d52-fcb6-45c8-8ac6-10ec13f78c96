package com.hailiang.edu.xsjlqueue.api.v1;

import com.hailiang.edu.xsjlqueue.dto.ResultJson;
import com.hailiang.edu.xsjlqueue.reqo.DemoReq;
import com.hailiang.edu.xsjlqueue.dto.UserInfo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
public interface DemoApi {

    @GetMapping(value = "/v1/demo/index")
    ResultJson index();

    @PostMapping(value = "/v1/demo/validate")
    ResultJson validate(DemoReq demoReq);


    @PostMapping(value = "/v1/demo/validate/login")
    ResultJson validateLogin(DemoReq demoReq,UserInfo userInfo);

}
package com.hailiang.edu.xsjlqueue.enums;


import lombok.Getter;


@SuppressWarnings("all")
@Getter
public enum SaasSectionEnum {

    YEY("1", "幼儿园"),
    XX("2", "小学"),
    CZ("3", "初中"),
    GZ("4", "高中"),
    ;


    SaasSectionEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;

    private String name;



    public static String getNameByCode(String code) {
        for (SaasSectionEnum gameEnum : SaasSectionEnum.values()) {
            if (code.equals(gameEnum.getCode())) {
                return gameEnum.getName();
            }
        }
        return "";
    }

}

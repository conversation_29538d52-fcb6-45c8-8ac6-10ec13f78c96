package com.hailiang.edu.xsjlqueue.exception;



import com.hailiang.edu.xsjlqueue.enums.ApiCodeEnum;

/**
 * 通用业务处理类
 * <AUTHOR>
 */
public class BusinessException extends Exception {
    private int code;
    private Object data;
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public BusinessException(String message){
        super(message);
        this.code = ApiCodeEnum.NORMAL_ERROR.getCode();
    }

    public BusinessException(String message, int code){
        super(message);
        this.code = code;
    }

    public BusinessException(String message, int code, Object data){
        super(message);
        this.code = code;
        this.data = data;
    }
}



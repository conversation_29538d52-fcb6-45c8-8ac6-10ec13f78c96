package com.hailiang.edu.xsjlqueue.util;

/**
 * Date：2023-01-18
 * Time：11:38
 * Description：redis key工具类
 *
 * <AUTHOR>
 */
public class RedisKeyUtil {
    public static final String SYS_CODE = "smsRuleqQueue";
    public static final String KEY_SEPARATOR = ":";

    public static String getRedisKey(Object... keys) {
        StringBuilder sb = new StringBuilder(SYS_CODE);
        if (keys != null && keys.length > 0) {
            for (Object key : keys) {
                sb.append(KEY_SEPARATOR).append(key);
            }
        }
        return sb.toString();
    }

}

package com.hailiang.edu.xsjlqueue.dto;



import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class UserInfo {

    @JSONField(ordinal = 1)
    private Long userId;

    @JSONField(ordinal = 2)
    private String phone;

    @JSONField(ordinal = 3)
    private String avatar;

    @JSONField(ordinal = 4)
    private String accountName;


    //saas相关 员工id 租户id
    @JSONField(ordinal = 5)
    private String staffId = "";

    @JSONField(ordinal = 6)
    private String tenantId = "";

    @JSONField(ordinal = 7)
    private String schoolId = "";


}


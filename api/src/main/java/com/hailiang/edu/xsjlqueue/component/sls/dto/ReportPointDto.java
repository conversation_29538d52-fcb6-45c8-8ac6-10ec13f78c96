package com.hailiang.edu.xsjlqueue.component.sls.dto;

import lombok.Data;

import java.util.List;

@Data
public class ReportPointDto {

    private Long smsReportId;

    private String smsReportName;

    private Long userId;

    private Integer familyStatus;

    private Long studentId;

    private String studentNo;

    private String studentName;

    private String saasClassId;

    private String saasClassName;

    private String saasSchoolId;

    private String saasSchoolName;

    //报告发送方式 (sms: 短信, wx:公众号)
    private String sendMethod;

    private String sendTime;

    private String createTime;

    private String updateTime;

    private List<FamilyMemberDto> familyMemberList;

    @Data
    public static class FamilyMemberDto {

        private String mobile;

        private String name;
    }


}

package com.hailiang.edu.xsjlqueue.util;





import com.hailiang.edu.xsjlqueue.exception.BusinessException;
import com.hailiang.edu.xsjlqueue.dto.LogFormat;
import com.hailiang.edu.xsjlqueue.dto.ResultJson;
import com.hailiang.hr.common.hr.threadCache.ReqThreadCache;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


@Component
@Log4j2
public class CommonUtil {


    @Value("${spring.application.name}")
    private String serverName;

    private void recordInfo(BusinessException businessException) {
        StackTraceElement stackTraceElement = businessException.getStackTrace()[0];
        LogFormat logFormat = new LogFormat();
        logFormat.setReqId(ReqThreadCache.getReqId());
        logFormat.setOp("info");
        logFormat.setServerName(serverName);
        logFormat.setFile(stackTraceElement.getClassName());
        logFormat.setFileLine(String.valueOf(stackTraceElement.getLineNumber()));
        logFormat.setMsg(businessException.getMessage());
        log.info(logFormat.format());
    }

    public ResultJson recordReturn(BusinessException businessException) {
        recordInfo(businessException);
        return new ResultJson(businessException.getCode(), businessException.getMessage(), businessException.getData());
    }

}






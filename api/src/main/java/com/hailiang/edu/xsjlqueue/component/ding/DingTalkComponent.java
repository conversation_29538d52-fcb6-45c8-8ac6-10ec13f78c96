package com.hailiang.edu.xsjlqueue.component.ding;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Comparator;
import java.util.List;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

@Component
public class DingTalkComponent {

    @Value("${dingRobot.secret}")
    private String secret;

    @Value("${dingRobot.sendUrl}")
    private String sendUrl;

    private String getServerUrl() {

        try {
            Long timestamp = System.currentTimeMillis();

            String stringToSign = timestamp + "\n" + this.secret;
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(this.secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");

            return this.sendUrl
                    + "&timestamp=" + timestamp + "&sign=" + sign;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public void send(String markDownText) {
        try {
            String serverUrl = getServerUrl();
            if (StrUtil.isEmpty(serverUrl)) {
                return;
            }
            DingTalkClient client = new DefaultDingTalkClient(serverUrl);
            OapiRobotSendRequest request = new OapiRobotSendRequest();

            request.setMsgtype("markdown");
            OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
            markdown.setTitle("学生数据同步");
            markdown.setText(markDownText);
            request.setMarkdown(markdown);

            OapiRobotSendResponse response = client.execute(request);

        } catch (Exception e) {
            e.printStackTrace();
        }

    }



}

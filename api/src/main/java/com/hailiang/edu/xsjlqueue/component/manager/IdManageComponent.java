package com.hailiang.edu.xsjlqueue.component.manager;


import com.hailiang.edu.xsjlqueue.util.RedisUtil;
import com.hailiang.edu.xsjlqueue.util.SnowFlakeUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Component
@Log4j2
public class IdManageComponent {

    @Resource
    RedisUtil redisUtil;

    private SnowFlakeUtil snowFlakeUtil;

    @PostConstruct
    public void init() {
        String cacheKey = "snowflake:report";

        Long increment = redisUtil.incr(cacheKey, 1L);
        //取余
        //例如 n %8 = n&7；也就是 n%a = n&（a-1）;
        //不是任何时候&都可以代替%来取模的！！！只有当n%a时只有a是2的幂函数的值时才能用 & 代替
        //也就是a = 2^k (k为整数）
        long machineId = increment & (32 - 1);
        log.info("snowflake machineId id is {}", machineId);
        snowFlakeUtil = new SnowFlakeUtil(machineId);
    }

    public Long nextId() {
        return snowFlakeUtil.nextId();
    }
}

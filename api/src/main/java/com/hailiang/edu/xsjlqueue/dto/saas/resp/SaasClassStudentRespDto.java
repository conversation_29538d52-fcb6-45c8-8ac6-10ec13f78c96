package com.hailiang.edu.xsjlqueue.dto.saas.resp;


import lombok.Data;

/**
 * <AUTHOR>
 * @version v0.1: SaasClassStudentRespDto.java, v 0.1 2023年09月19日 18:00  zhousx Exp $
 */
@Data
public class SaasClassStudentRespDto {

    /**
     * 学生id
     */
    private Long studentId;

    /**
     * 学生名称
     */
    private String studentName;

    /**
     * 学生学号
     */
    private String studentNo;

    /**
     * 性别：0未知；1男；2女
     */
    private Integer sex;

    /**
     * 行政班班级id
     */
    private Long classId;

    /**
     * 行政班班级名称
     */
    private String className;

    /**
     * 行政班班级别名
     */
    private String classAlias;

    /**
     * 学校ID
     */
    private Long schoolId;

    /**
     * 是否分配教学班
     */
    private Boolean isDisTea;

    /**
     * 学生绑定班级时间
     */
    private Long studentJoinTime;

    /**
     * 学生状态0正常1休学
     */
    private String studentStatus;

}

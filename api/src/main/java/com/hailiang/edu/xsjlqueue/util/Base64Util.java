package com.hailiang.edu.xsjlqueue.util;



import java.util.Base64;

public class Base64Util {

    public static String encodeToStr(String s)
    {
        Base64.Encoder encoder = Base64.getEncoder();
        return encoder.encodeToString(s.getBytes());
    }

    public static String decodeToStr(String s)
    {
        Base64.Decoder decoder = Base64.getDecoder();
        return new String(decoder.decode(s));
    }

    public static String encodeToByte(byte[] s)
    {
        Base64.Encoder encoder = Base64.getEncoder();
        return encoder.encodeToString(s);
    }

    public static byte[] decodeToByte(byte[] s)
    {
        Base64.Decoder decoder = Base64.getDecoder();
        return decoder.decode(s);
    }
}




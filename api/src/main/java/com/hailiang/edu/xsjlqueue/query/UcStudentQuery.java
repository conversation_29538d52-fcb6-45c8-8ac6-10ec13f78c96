/**
 * Hailiang.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.hailiang.edu.xsjlqueue.query;

import com.hailiang.hr.common.saas.req.BasePageQueryReq;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v0.1: UcStudentQuery.java, v 0.1 2023年09月20日 20:08  zhousx Exp $
 */
@Data
public class UcStudentQuery extends BasePageQueryReq {
    private Long id;
    private List<Long> ids;
    private Long tenantId;
    private String name;
    private Integer sex;
    private String studentNo;
    private Long schoolId;
    private Long userId;
    private Long idAbove;
    private String orderBy;
    private Date gmtCreated;
    private Date gmtModified;
    private Boolean isDeleted;
    private String creator;
    private String modifier;
    private Map<String, String> likeMap;
}
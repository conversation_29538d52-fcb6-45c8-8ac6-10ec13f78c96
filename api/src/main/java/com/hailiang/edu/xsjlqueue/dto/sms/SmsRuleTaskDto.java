package com.hailiang.edu.xsjlqueue.dto.sms;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class SmsRuleTaskDto {

    /**
     * 任务id 雪花算法
     */
    @JSONField(ordinal = 1)
    private Long smsRuleTaskId;


    /**
     * 规则id
     */
    @JSONField(ordinal = 2)
    private Long smsRuleId;

    /**
     * 开始时间
     */
    @JSONField(ordinal = 3)
    private String createTime;


    /**
     * 更新时间
     */
    @JSONField(ordinal = 4)
    private String updateTime;
}

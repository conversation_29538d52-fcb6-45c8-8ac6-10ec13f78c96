package com.hailiang.edu.xsjlqueue.component.sls;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.exception.LogException;
import com.hailiang.edu.xsjlqueue.component.manager.IdManageComponent;
import com.hailiang.edu.xsjlqueue.component.sls.dto.ReportPointDto;
import com.hailiang.edu.xsjlqueue.config.SlsConfig;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class SlsComponent {

    @Resource
    SlsConfig slsConfig;
    @Resource
    Client client;
    @Resource
    IdManageComponent idManageComponent;


    public void pushLog(List<ReportPointDto> reportPointDtoList) throws LogException {

        if (CollectionUtils.isEmpty(reportPointDtoList)) {
            return;
        }

        List<LogItem> logGroup = new ArrayList<LogItem>();

        for (ReportPointDto reportPointDto : reportPointDtoList) {
            LogItem logItem = new LogItem();
            logItem.PushBack("_ua_", "");
            logItem.PushBack("_width_","");
            logItem.PushBack("_height_","");
            logItem.PushBack("_href_","");
            logItem.PushBack("_time_", String.valueOf(DateUtil.current()));
            logItem.PushBack("_title_","报告发送情况");
            logItem.PushBack("_uuid_",String.valueOf(idManageComponent.nextId()));
            logItem.PushBack("_terminal_","");
            logItem.PushBack("_terminal_info_","");
            logItem.PushBack("_level_","INFO");
            logItem.PushBack("_userId_",String.valueOf(reportPointDto.getUserId()));
            logItem.PushBack("_type_","3");
            logItem.PushBack("_terminal_","");
            logItem.PushBack("_event_code_","XDL_SERVER_001");
            logItem.PushBack("_event_info_", JSONObject.toJSONString(reportPointDto));
            logItem.PushBack("_business_info_","");

            logGroup.add(logItem);
        }

        client.PutLogs(slsConfig.getProjectName(), slsConfig.getLogstoreName(), "", logGroup, "");
    }
}

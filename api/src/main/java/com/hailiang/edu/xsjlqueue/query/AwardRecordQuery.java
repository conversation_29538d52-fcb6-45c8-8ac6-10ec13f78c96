package com.hailiang.edu.xsjlqueue.query;

import lombok.Data;

import java.util.Set;

@Data
public class AwardRecordQuery {

    private Long planId;

    private Long awardRecordId;

    private Long awardRuleId;

    /**
     * 时间是否重叠，1是 0不是
     */
    private Integer isDuplicateTime;

    private String startTime;

    private String endTime;

    private Long userId;

    private String saasClassId;

    private Integer includeDeleted;

    private Set<Long> planIds;

}

package com.hailiang.edu.xsjlqueue.component.jwt;

import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;

import com.hailiang.edu.xsjlqueue.dto.UserInfo;
import com.hailiang.edu.xsjlqueue.util.DateUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 组件 jwt 处理类
 *
 * <AUTHOR>
 */
@Component
@Log4j2
public class JwtToken {

    private String appKey = "abc123";

    public UserInfo decodeUserInfo(String eduToken) {
        try {
            JWTVerifier verifier = JWT.require(Algorithm.HMAC256(appKey)).build();
            DecodedJWT jwt = verifier.verify(eduToken);
            String userInfoJson = jwt.getClaim("userInfoJson").asString();
            if (null != userInfoJson) {
                return JSONObject.parseObject(userInfoJson, UserInfo.class);
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    public String encodeUserInfo(UserInfo userInfo) {
        try {
            //token 生成 有效期为 7天先
            long time = DateUtil.getCurrentTimeStamp() + 60 * 60 * 24 * 1000 * 7;

            Date expiresDate = new Date(time);

            Map<String, Object> map = new HashMap<String, Object>();
            map.put("alg", "HS256");
            map.put("typ", "JWT");
            return JWT.create()
                    .withHeader(map)
                    .withClaim("userInfoJson", JSONObject.toJSONString(userInfo))
                    .withExpiresAt(expiresDate)
                    .sign(Algorithm.HMAC256(appKey));
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return null;
    }

    /**
     * 验证教学登陆是否有效
     *
     * @param token
     * @return
     */
    public boolean isValidEduToken(String token) {
        try {
            JWTVerifier verifier = JWT.require(Algorithm.HMAC256(appKey)).build();
            verifier.verify(token);

            return true;
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return false;
    }


}
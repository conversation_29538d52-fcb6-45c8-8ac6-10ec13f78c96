package com.hailiang.edu.xsjlqueue.dto.alibaba;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiWriterOutputVO {
    /**
     * 对话历史会话唯一标识
     */
    @JsonProperty("session_id")
    private String sessionId;

    /**
     * 正在生成时为null，生成结束时如果由于停止token导致则为stop
     */
    @JsonProperty("finish_reason")
    private String finishReason;

    /**
     * 模型生成回复
     */
    @JsonProperty("text")
    private String text;
}
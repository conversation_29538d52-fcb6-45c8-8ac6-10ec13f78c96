package com.hailiang.edu.xsjlqueue.remote.biz.saas;

/**
 * 工具类
 * <AUTHOR>
 */
public final class Constant {
    public static final String USER_ID = "userId";
    public static final String APP_ID = "appId";
    public static final String APP_SECRET = "appSecret";
    public static final String STAFF_ID = "staffId";
    public static final String SCHOOL_ID = "schoolId";
    public static final String TENANT_ID = "tenantId";
    public static final String AUTHORIZATION = "Authorization";
    public static final String ACCESS_TOKEN = "accessToken";
    public static final String SIGN = "sign";
    public static final String METHODAUTH = "methodAuth";
    public static final String SITE = "site";
    public static final String RESCODE = "resCode";
    public static final String RESDESC = "resDesc";
    public static final String POST = "POST";
    public static final String GET = "GET";
    public static final String TIMESTAMP = "timestamp";
    public static final String NONCE = "nonce";
    public static final String LANGUAGE = "language";




    public static final Integer SUCCESS_CODE = 200;

    //错误次数达到需要图片验证码验证
    public static final Integer ERROR_CODE_VERIFICATION_LOGIN_FAILD = 10001010;
    //图片验证码错误
    public static final Integer ERROR_CODE_VERIFICATION_FAILD = 10001;

    //错误次数达到锁定账号情况
    public static final Integer ERROR_CODE_LOCK = 10003;

}

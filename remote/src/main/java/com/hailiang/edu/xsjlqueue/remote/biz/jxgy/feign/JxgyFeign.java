package com.hailiang.edu.xsjlqueue.remote.biz.jxgy.feign;



import com.hailiang.edu.xsjlqueue.dto.jxgy.req.FamilyStatusReq;
import com.hailiang.edu.xsjlqueue.dto.jxgy.req.SendWxTemplateMsgReq;
import com.hailiang.edu.xsjlqueue.dto.jxgy.resp.FamilyStatusResp;
import com.hailiang.edu.xsjlqueue.dto.jxgy.resp.ResultRespDto;
import com.hailiang.edu.xsjlqueue.dto.jxgy.resp.SendWxTemplateMsgResp;
import com.hailiang.edu.xsjlqueue.remote.biz.config.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * https://alidocs.dingtalk.com/i/nodes/3xRN9bGQyw4JbxOO6vjzWzXPADKnorv6?doc_type=wiki_doc&dontjump=true#%20%E3%80%8C%E5%AF%B9%E5%A4%96%E6%8F%90%E4%BE%9B%E6%8E%A5%E5%8F%A3%E6%96%87%E6%A1%A3%E3%80%8D
 */
@FeignClient(url = "${jxgy.host}", name = "JxgyInner", configuration = FeignConfiguration.class)
public interface JxgyFeign {


    /**
     * 获取学生家长开通情况以及家长列表
     * @param familyStatusReq
     * @return
     */
    @PostMapping("/third/student/getFamilyStatus")
    ResultRespDto<List<FamilyStatusResp>> getFamilyStatus(@RequestBody FamilyStatusReq familyStatusReq);

    /**
     * 获取学生家长开通情况以及家长列表 根据saasStudentIds
     * @param familyStatusReq
     * @return
     */
    @PostMapping("/third/student/get_family_by_student_id")
    ResultRespDto<List<FamilyStatusResp>> getFamilyByStudentIds(@RequestBody FamilyStatusReq familyStatusReq);

    /**
     * 小程序消息发送接口
     * @param sendWxTemplateMsgReq
     * @return
     */
    @PostMapping("/third/wxNotice/sendTemplateMsg")
    ResultRespDto<List<SendWxTemplateMsgResp>> sendWxTemplateMsg(@RequestBody SendWxTemplateMsgReq sendWxTemplateMsgReq);
}

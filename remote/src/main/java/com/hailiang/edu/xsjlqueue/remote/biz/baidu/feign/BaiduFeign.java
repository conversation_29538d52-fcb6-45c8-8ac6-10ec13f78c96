package com.hailiang.edu.xsjlqueue.remote.biz.baidu.feign;

import com.hailiang.aimodel.api.model.entity.baidu.ErnieBot;
import com.hailiang.aimodel.api.model.vo.baidu.ErnieBotVO;
import com.hailiang.edu.xsjlqueue.remote.biz.config.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 百度ai
 */
@FeignClient(url = "${baidu.aimodel.host}", name = "BaiduAiInner", configuration = FeignConfiguration.class)
public interface BaiduFeign {

    /**
     * 生成智能评语接口 Bot
     * @param dto
     * @return
     */
    @PostMapping("/model/completion/ERNIEBOT")
    ErnieBotVO ernieBotCompletion(@RequestBody ErnieBot dto);

    /**
     * 生成智能评语接口 Bot
     * @param dto
     * @return
     */
    @PostMapping("/model/completion/ERNIEBOT4")
    ErnieBotVO ernieBotCompletionFor(@RequestBody ErnieBot dto);

    /**
     * 生成智能评语接口 turbo
     * @param dto
     * @return
     */
    @PostMapping("/model/completion/ERNIEBOTTURBO")
    ErnieBotVO ernieBotCompletionTurbo(@RequestBody ErnieBot dto);
}

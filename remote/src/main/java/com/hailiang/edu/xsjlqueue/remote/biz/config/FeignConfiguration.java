package com.hailiang.edu.xsjlqueue.remote.biz.config;


import feign.Logger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;


/**
 * <AUTHOR>
 * @Date 9:16 2022/7/25
 * @Version 1.0
 **/
@Slf4j
public class FeignConfiguration {

/*    @Bean
    Logger.Level loggerLevel() {
        //这里记录所有，根据实际情况选择合适的日志level
        return Logger.Level.FULL;
    }

    @Bean
    Logger feignLogger(){
        return new FeignLogger();
    }*/

}
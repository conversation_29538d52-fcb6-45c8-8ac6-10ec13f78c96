package com.hailiang.edu.xsjlqueue.remote.biz.saas.feign;

import com.hailiang.edu.xsjlqueue.dto.saas.resp.*;
import com.hailiang.edu.xsjlqueue.query.UcStudentQuery;
import com.hailiang.edu.xsjlqueue.remote.biz.config.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * saas对外
 */
@FeignClient(url = "${open.feign.sys}", name = "saasClient", configuration = FeignConfiguration.class)
public interface SaasFeign {

    @PostMapping({"/open-web-bff/open-web-bff/web-bff/tchSchool/queryById"})
    CommonResultRespDto<SaasSchoolRespDto> queryById(@RequestParam("id") Long var1);

    /**
     * 查询学生信息
     * @param query
     * @return
     */
    @PostMapping({"/open-web-bff/open-web-bff/web-bff/ucStudent/queryPageByCondition"})
    CommonResultRespDto<PageResultRespDto<UcStudentRespDto>> queryPageByCondition(@RequestBody UcStudentQuery query);

    @PostMapping({"/open-web-bff/open-web-bff/web-bff/tchClassInfo/by-ids"})
    CommonResultRespDto<List<SaasClassRespDto>> queryClassListByIds(@RequestBody List<Long> classIds);
}

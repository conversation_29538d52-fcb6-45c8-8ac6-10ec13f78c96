package com.hailiang.edu.xsjlqueue.remote.biz.saas;

/*
 * @description: 公共枚举类
 * <AUTHOR>
 * @date 2021/10/12
 */
@SuppressWarnings("all")
public class CommonEnum {
    /*
     * @description: 学段枚举
     * <AUTHOR>
     * @date 2021/10/12
     */
    public enum AuthEnum {
        验证成功("101","验证通过"),
        应用令牌不正确("102","应用令牌不正确"),
        签名不正确("103","签名不正确"),
        用户令牌不正确("104","用户令牌不正确"),
        头信息参数不正确("201","头信息参数不正确"),
        ;
        private String code;

        private String desc;

        AuthEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}

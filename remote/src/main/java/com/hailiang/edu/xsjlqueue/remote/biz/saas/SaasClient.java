package com.hailiang.edu.xsjlqueue.remote.biz.saas;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.hailiang.edu.xsjlqueue.query.UcStudentQuery;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import com.hailiang.edu.xsjlqueue.dto.saas.req.*;
import com.hailiang.edu.xsjlqueue.dto.saas.resp.*;
import com.hailiang.edu.xsjlqueue.remote.biz.saas.feign.SaasFeign;
import com.hailiang.edu.xsjlqueue.remote.biz.saas.feign.SaasFeignInner;
import com.hailiang.edu.xsjlqueue.util.LogFormatUtil;
import cn.hutool.core.collection.CollUtil;
import lombok.extern.log4j.Log4j2;

@Component
@Log4j2
@SuppressWarnings("all")
public class SaasClient {

    @Value("${saas.appId}")
    private String appId;

    @Value("${saas.appSecret}")
    private String appSecret;

    @Value("${saas.accessToken}")
    private String accessToken;

    @Value("${saas.openUrl}")
    private String openUrl;

    @Value("${saas.defaultSite}")
    private String defaultSite;

    @Resource
    LogFormatUtil  logFormatUtil;

    @Resource
    SaasFeignInner saasFeignInner;

    @Resource
    SaasFeign      saasFeign;

    public Map<Long, Long> getUserIdMapStaffId(Map<Long, Long> userIdWithOpenIdMap, String saasTenantId) {
        try {
            if (CollectionUtils.isEmpty(userIdWithOpenIdMap)) {
                return new HashMap<>();
            }
            StaffUnderTenantReqDto staffUnderTenantReqDto = new StaffUnderTenantReqDto();
            staffUnderTenantReqDto.setTenantId(Long.valueOf(saasTenantId));
            List<Long> userIdList = new ArrayList<>();
            for (Map.Entry<Long, Long> longStringEntry : userIdWithOpenIdMap.entrySet()) {
                userIdList.add(longStringEntry.getValue());
            }
            staffUnderTenantReqDto.setUserIdList(userIdList);
            CommonResultRespDto<List<StaffUnderTenantRespDto>> commonResultRespDto = saasFeignInner
                .getSaasStaffListUnderTenant(staffUnderTenantReqDto);
            if (commonResultRespDto.getSuccess() && commonResultRespDto.getStatus().equals(200)) {
                List<StaffUnderTenantRespDto> staffUnderTenantRespDtoList = commonResultRespDto.getData();
                Map<Long, Long> saasUserIdMapStaffId = staffUnderTenantRespDtoList.stream().collect(Collectors
                    .toMap(StaffUnderTenantRespDto::getUserId, StaffUnderTenantRespDto::getStaffId, (p1, p2) -> p2));
                if (!CollectionUtils.isEmpty(saasUserIdMapStaffId)) {
                    Map<Long, Long> userIdMapStaffId = new HashMap<>();
                    for (Map.Entry<Long, Long> longStringEntry : userIdWithOpenIdMap.entrySet()) {
                        if (saasUserIdMapStaffId.containsKey(longStringEntry.getValue())) {
                            Long staffId = saasUserIdMapStaffId.get(longStringEntry.getValue());
                            userIdMapStaffId.put(longStringEntry.getKey(), staffId);
                        }
                    }
                    return userIdMapStaffId;
                }
            }
        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
        }
        return new HashMap<>();
    }

    public List<TeachManageStaffTeachRespDto> getTeachManageStaffTeachListByCondition(SaasTeachManageStaffTeachReqDto saasTeachManageStaffTeachListReqDto) {

        try {
            CommonResultRespDto<List<TeachManageStaffTeachRespDto>> commonResultRespDto = saasFeignInner
                .getSaasTeachManageStaffTeachListByCondition(saasTeachManageStaffTeachListReqDto);
            if (commonResultRespDto.getSuccess() && commonResultRespDto.getStatus().equals(200)) {
                return commonResultRespDto.getData();
            }

        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
        }

        return new ArrayList<>();
    }

    public List<StaffUnderTenantRespDto> getSaasStaffListUnderTenant(StaffUnderTenantReqDto staffUnderTenantReqDto) {

        try {
            CommonResultRespDto<List<StaffUnderTenantRespDto>> commonResultRespDto = saasFeignInner
                .getSaasStaffListUnderTenant(staffUnderTenantReqDto);
            if (commonResultRespDto.getSuccess() && commonResultRespDto.getStatus().equals(200)) {
                return commonResultRespDto.getData();
            }

        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
        }

        return new ArrayList<>();
    }

    public List<UnderClassInfoRespDto> getUnderClassInfoList(UnderClassInfoReqDto underClassInfoReqDto) {

        try {
            CommonResultRespDto<List<UnderClassInfoRespDto>> commonResultRespDto = saasFeignInner
                .getUnderClassInfoList(underClassInfoReqDto);
            if (commonResultRespDto.getSuccess() && commonResultRespDto.getStatus().equals(200)) {
                return commonResultRespDto.getData();
            }

        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
        }

        return new ArrayList<>();
    }

    public List<ParentListRespDto> getParentListByCondition(Set<Long> studentIds) {
        try {

            if (CollUtil.isEmpty(studentIds)) {
                return new ArrayList<>();
            }

            SaasParentListReqDto saasParentListReqDto = new SaasParentListReqDto();
            saasParentListReqDto.setStudentIds(studentIds);
            CommonResultRespDto<List<ParentListRespDto>> commonResultRespDto = saasFeignInner
                .getParentListByCondition(saasParentListReqDto);
            if (commonResultRespDto.getSuccess() && commonResultRespDto.getStatus().equals(200)) {
                List<ParentListRespDto> parentListRespDtoList = commonResultRespDto.getData();
                if (!CollectionUtils.isEmpty(parentListRespDtoList)) {
                    return parentListRespDtoList;
                }
            }
        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
        }
        return new ArrayList<>();
    }

    public List<SaasClassRespDto> getSchoolClassList(SaasSchoolClassQueryReqDto saasSchoolClassQueryReqDto) {
        try {
            CommonResultRespDto<List<SaasClassRespDto>> commonResultRespDto = saasFeignInner
                .getSchoolClassList(saasSchoolClassQueryReqDto);
            if (commonResultRespDto.getSuccess() && commonResultRespDto.getStatus().equals(200)) {
                return commonResultRespDto.getData();
            }

        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
        }
        return new ArrayList<>();
    }

    public SaasSchoolRespDto getSchoolInfo(Long schoolId) {

        try {
            CommonResultRespDto<SaasSchoolRespDto> tchSchoolRespDtoCommonResultRespDto = saasFeign.queryById(schoolId);

            //            logFormatUtil.formatInfo("tchSchoolRespDtoCommonResultRespDto: " + JSONObject.toJSONString(tchSchoolRespDtoCommonResultRespDto));

            if (tchSchoolRespDtoCommonResultRespDto.getSuccess()
                && tchSchoolRespDtoCommonResultRespDto.getStatus().equals(200)) {
                return tchSchoolRespDtoCommonResultRespDto.getData();
            }
        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
        }
        return null;
    }

    public List<SaasClassStudentRespDto> queryClassStudentList(SaasClassStudentPageQueryReqDto saasClassStudentPageQueryReqDto) {
        List<SaasClassStudentRespDto> result = new ArrayList<>();
        // 1.因为接口限制一次最多只能查询5000条，所以如果超过5000条会循环去查询（基于此处查询班级下学生的场景，班级下学生不可能超过5000，
        // 故去除此处循环查询，如果需要用此方法查询学校下的所有学生还是另外写一个吧！）
        try {
            CommonResultRespDto<PageResultRespDto<SaasClassStudentRespDto>> commonResultRespDto = saasFeignInner
                .queryClassStudentPageByCondition(saasClassStudentPageQueryReqDto);
            if (commonResultRespDto.getSuccess() && commonResultRespDto.getStatus().equals(200)) {
                result.addAll(commonResultRespDto.getData().getList());
                /*if (commonResultRespDto.getData().getPageCount() > saasClassStudentPageQueryReqDto.getPageNum()) {
                    saasClassStudentPageQueryReqDto.setPageNum(saasClassStudentPageQueryReqDto.getPageNum() + 1);
                    result.addAll(queryClassStudentList(saasClassStudentPageQueryReqDto));
                }*/
            }
        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
        }
        return result;

    }

    public List<UcStudentRespDto> queryStudentList(UcStudentQuery query) {

        List<UcStudentRespDto> ucStudentRespDtoList = new ArrayList<>();
        try {
            CommonResultRespDto<PageResultRespDto<UcStudentRespDto>> pageResultRespDtoCommonResultRespDto = saasFeign
                .queryPageByCondition(query);
            if (pageResultRespDtoCommonResultRespDto != null) {
                if (pageResultRespDtoCommonResultRespDto.getSuccess()
                    && pageResultRespDtoCommonResultRespDto.getStatus().equals(200)) {
                    ucStudentRespDtoList = pageResultRespDtoCommonResultRespDto.getData().getList();
                }
            }
        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
        }
        return ucStudentRespDtoList;
    }

    public List<SaasClassRespDto> queryClassListByIds(List<Long> classIds) {

        List<SaasClassRespDto> ucStudentRespDtoList = new ArrayList<>();
        try {
            CommonResultRespDto<List<SaasClassRespDto>> resultRespDtoCommonResultRespDto = saasFeign
                .queryClassListByIds(classIds);
            if (resultRespDtoCommonResultRespDto != null) {
                if (resultRespDtoCommonResultRespDto.getSuccess()
                    && resultRespDtoCommonResultRespDto.getStatus().equals(200)) {
                    ucStudentRespDtoList = resultRespDtoCommonResultRespDto.getData();
                }
            }
        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
        }
        return ucStudentRespDtoList;
    }

}

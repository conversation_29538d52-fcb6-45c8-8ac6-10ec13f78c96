package com.hailiang.edu.xsjlqueue.remote.biz.baidu;

import com.hailiang.aimodel.api.model.entity.baidu.ChatMessage;
import com.hailiang.aimodel.api.model.entity.baidu.ErnieBot;
import com.hailiang.aimodel.api.model.vo.baidu.ErnieBotVO;
import com.hailiang.edu.xsjlqueue.remote.biz.baidu.feign.BaiduFeign;
import com.hailiang.edu.xsjlqueue.util.LogFormatUtil;
import org.springframework.retry.RetryException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class BaiduAiClient {

    @Resource
    LogFormatUtil logFormatUtil;

    @Resource
    BaiduFeign baiduFeign;

    @Retryable(value = RetryException.class, maxAttempts = 3, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public String getStudentEvaluateData(String askData) {

        try {

            List<ChatMessage> chatMessageList = new ArrayList<>();
            ChatMessage chatMessage = new ChatMessage();
            chatMessage.setRole("user");
            chatMessage.setContent(askData);

            chatMessageList.add(chatMessage);

            ErnieBot ernieBot = new ErnieBot();
            ernieBot.setStream(Boolean.FALSE);
            ernieBot.setMessages(chatMessageList);
            ErnieBotVO ernieBotVOResultRespDto = baiduFeign.ernieBotCompletionTurbo(ernieBot);

            if (ernieBotVOResultRespDto != null) {
                return ernieBotVOResultRespDto.getResult();
            }

        } catch (RetryException e) {
            logFormatUtil.exceptionPrint(e);
            return "";
        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
            return "";
        }
        return "";
    }

}

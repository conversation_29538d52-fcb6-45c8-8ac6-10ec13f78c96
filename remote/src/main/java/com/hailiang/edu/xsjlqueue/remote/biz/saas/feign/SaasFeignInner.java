package com.hailiang.edu.xsjlqueue.remote.biz.saas.feign;


import com.hailiang.edu.xsjlqueue.dto.saas.req.*;
import com.hailiang.edu.xsjlqueue.dto.saas.resp.*;
import com.hailiang.edu.xsjlqueue.remote.biz.config.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * saas 对内
 */
@FeignClient(url = "${open.feign.inner}", name = "SaasFeignInner", configuration = FeignConfiguration.class)
public interface SaasFeignInner {


    /**
     * 查询学校下教职工的任职情况
     * http://10.30.5.48:40001/project/221/interface/api/23186
     *
     * @param saasTeachManageStaffTeachListReqDto
     * @return
     */
    @PostMapping("/educational/teachManage/v1/staff/teach/list")
    CommonResultRespDto<List<TeachManageStaffTeachRespDto>> getSaasTeachManageStaffTeachListByCondition(@RequestBody SaasTeachManageStaffTeachReqDto saasTeachManageStaffTeachListReqDto);


    /**
     * 根据租户ID、用户id集合查询教职工列表
     * http://10.30.5.48:40001/project/221/interface/api/29356
     *
     * @param staffUnderTenantReqDto
     * @return
     */
    @PostMapping("/staff/v2/list/under/tenant")
    CommonResultRespDto<List<StaffUnderTenantRespDto>> getSaasStaffListUnderTenant(@RequestBody StaffUnderTenantReqDto staffUnderTenantReqDto);


    /**
     * 根据班级集合查询班级信息
     * http://10.30.5.48:40001/project/221/interface/api/24911
     * @param underClassInfoReqDto
     * @return
     */
    @PostMapping("/classInfo/v1/list/under/by_class_ids")
    CommonResultRespDto<List<UnderClassInfoRespDto>> getUnderClassInfoList(@RequestBody UnderClassInfoReqDto underClassInfoReqDto);

    /**
     * 根据学生id集合获取他们的家长信息
     * http://10.30.5.48:40001/project/221/interface/api/20791
     *
     * @param saasParentListReqDto
     * @return
     */
    @PostMapping("/educational/teachManage/v1/parent/list")
    CommonResultRespDto<List<ParentListRespDto>> getParentListByCondition(@RequestBody SaasParentListReqDto saasParentListReqDto);

    @PostMapping({"/educational/teachManage/v1/class/list"})
    CommonResultRespDto<List<SaasClassRespDto>> getSchoolClassList(SaasSchoolClassQueryReqDto saasSchoolClassQueryReqDto);

    /**
     * 班级学生分页查询
     *
     * @return List<SaasClassStudentRespDto>
     */
    @PostMapping({"/student/v1/query_class_student_page_by_condition"})
    CommonResultRespDto<PageResultRespDto<SaasClassStudentRespDto>> queryClassStudentPageByCondition(SaasClassStudentPageQueryReqDto saasClassStudentPageQueryReqDto);

}

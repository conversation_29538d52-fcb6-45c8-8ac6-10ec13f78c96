package com.hailiang.edu.xsjlqueue.remote.biz.jxgy;


import com.alibaba.fastjson.JSONObject;
import com.hailiang.edu.xsjlqueue.component.manager.IdManageComponent;
import com.hailiang.edu.xsjlqueue.dto.jxgy.req.FamilyStatusReq;
import com.hailiang.edu.xsjlqueue.dto.jxgy.req.SendWxTemplateMsgReq;
import com.hailiang.edu.xsjlqueue.dto.jxgy.resp.FamilyStatusResp;
import com.hailiang.edu.xsjlqueue.dto.jxgy.resp.ResultRespDto;
import com.hailiang.edu.xsjlqueue.dto.jxgy.resp.SendWxTemplateMsgResp;
import com.hailiang.edu.xsjlqueue.remote.biz.jxgy.feign.JxgyFeign;
import com.hailiang.edu.xsjlqueue.util.DateUtil;
import com.hailiang.edu.xsjlqueue.util.LogFormatUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class JxgyClient {

    @Resource
    JxgyFeign jxgyFeign;

    @Resource
    LogFormatUtil logFormatUtil;

    @Resource
    IdManageComponent idManageComponent;

    @Value("${jxgy.appId}")
    String appId;


    public List<FamilyStatusResp> getFamilyStatusList(FamilyStatusReq familyStatusReq) {


        //todo 暂时mock处理
//        String testJson = "[{\"familyMember\":[{\"mobile\":\"15238341624\",\"name\":\"王震\"}],\"familyStatus\":1,\"studentCode\":\"17308\",\"studentName\":\"王饱饱\"}]";
//        return JSONArray.parseArray(testJson, FamilyStatusResp.class);

       try {
            familyStatusReq.setAppid(appId);
            familyStatusReq.setTimestamp(String.valueOf(DateUtil.getCurrentTimeStamp() / 1000));
            familyStatusReq.setNonceStr(String.valueOf(idManageComponent.nextId()));
            familyStatusReq.setSign(familyStatusReq.getNonceStr());

            ResultRespDto<List<FamilyStatusResp>> resultRespDto = jxgyFeign.
                    getFamilyStatus(familyStatusReq);
            if (resultRespDto.getCode().equals(200) ) {
                return resultRespDto.getData();
            }
        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
        }
        return new ArrayList<>();
    }

    public List<FamilyStatusResp> getFamilyStatusListByStudentIds(FamilyStatusReq familyStatusReq) {

        try {
            familyStatusReq.setAppid(appId);
            familyStatusReq.setTimestamp(String.valueOf(DateUtil.getCurrentTimeStamp() / 1000));
            familyStatusReq.setNonceStr(String.valueOf(idManageComponent.nextId()));
            familyStatusReq.setSign(familyStatusReq.getNonceStr());

            ResultRespDto<List<FamilyStatusResp>> resultRespDto = jxgyFeign.
                    getFamilyByStudentIds(familyStatusReq);
            if (resultRespDto.getCode().equals(200) ) {
                return resultRespDto.getData();
            }
        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
        }
        return new ArrayList<>();
    }


    public List<SendWxTemplateMsgResp> sendWxTemplateMsg(SendWxTemplateMsgReq sendWxTemplateMsgReq) {

        try {
            sendWxTemplateMsgReq.setAppid(appId);
            sendWxTemplateMsgReq.setTimestamp(String.valueOf(DateUtil.getCurrentTimeStamp() / 1000));
            sendWxTemplateMsgReq.setNonceStr(String.valueOf(idManageComponent.nextId()));
            sendWxTemplateMsgReq.setSign(sendWxTemplateMsgReq.getNonceStr());

            ResultRespDto<List<SendWxTemplateMsgResp>> resultRespDto = jxgyFeign.
                    sendWxTemplateMsg(sendWxTemplateMsgReq);
            if (resultRespDto.getCode().equals(200)) {
                logFormatUtil.formatInfo("家校接口返回值:" + JSONObject.toJSONString(resultRespDto));

                return resultRespDto.getData();
            }
        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
        }
        return new ArrayList<>();

    }


}

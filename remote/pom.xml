<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.hailiang.edu</groupId>
        <artifactId>xsjlqueue-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>xsjlqueue-remote</artifactId>
    <name>xsjlqueue-remote</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.hailiang.edu</groupId>
            <artifactId>xsjlqueue-api</artifactId>
            <version>${api.version}</version>
        </dependency>
        <!-- 重试策略-->
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
    </dependencies>

</project>

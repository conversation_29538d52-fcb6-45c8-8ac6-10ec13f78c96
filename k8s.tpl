---
apiVersion: v1
kind: Service
metadata:
  name: {{APP_NAME}}
  namespace: {{NAMESPACE}}
spec:
  type: ClusterIP
  ports:
  - port: {{PORT}}
    protocol: TCP
    targetPort: {{PORT}}
  selector:
    app: {{APP_NAME}}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{APP_NAME}}
  namespace: {{NAMESPACE}}
spec:
  selector:
    matchLabels:
      app: {{APP_NAME}}
  replicas: {{NUM}}
  template:
    metadata:
      labels:
        app: {{APP_NAME}}
        logType: app
        optl-agent: java
      annotations:
        instrumentation.opentelemetry.io/inject-java: opentelemetry-operator-system/opentelemetry-autoinstrumentation
    spec:
      enableServiceLinks: false
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 2000
        fsGroup: 2000
      containers:
        - name: {{APP_NAME}}
          env:
            - name: TZ
              value: Asia/Shanghai
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: POD_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: OTEL_SERVICE_NAME
              value:  {{APP_NAME}}
            - name: OTEL_TRACES_EXPORTER
              value: otlp
          image: {{IMAGE_URL}}:{{IMAGE_TAG}}
          imagePullPolicy: IfNotPresent
          resources:
            requests:
              memory: 512Mi
              cpu: 100m
            limits:
              memory: 2Gi
              cpu: 2
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
            - name: app-logs
              mountPath: /opt/app/logs    #日志目录
              subPathExpr: $(POD_NAMESPACE)/$(POD_NAME)
          ports:
            - containerPort: {{PORT}}
              name: port
              protocol: TCP
            - containerPort: 19642
              name: http-metrics
              protocol: TCP
          command: ["/bin/sh"]
          args: ["-c", 'set -e && java {{jvm_options}} -Dlogging.pattern.level="trace_id=%mdc{trace_id} span_id=%mdc{span_id} trace_flags=%mdc{trace_flags} %5p" -jar  {{APP_JAR}} ']
          lifecycle:
            preStop:
              exec:
                command: ['/bin/sh','-c','/opt/app/nacosDown.sh']
          readinessProbe:
            tcpSocket:
              port: {{PORT}}
            initialDelaySeconds: 10
            timeoutSeconds: 5
            periodSeconds: 5
            successThreshold: 1
            failureThreshold: 10
          livenessProbe:
            tcpSocket:
              port: {{PORT}}
            initialDelaySeconds: 45
            timeoutSeconds: 5
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 12
          startupProbe:
            tcpSocket:
              port: {{PORT}}
            initialDelaySeconds: 20
            periodSeconds: 5
            failureThreshold: 10
            successThreshold: 1
            timeoutSeconds: 5
      terminationGracePeriodSeconds: 120
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - {{APP_NAME}}
            topologyKey: kubernetes.io/hostname
      volumes:
        - name: host-time
          hostPath:
            path: /etc/localtime
            type: ''
        - name: app-logs
          hostPath:
            path: /var/lib/containerd/applogs
            type: DirectoryOrCreate
      imagePullSecrets:
      - name: swr-secret

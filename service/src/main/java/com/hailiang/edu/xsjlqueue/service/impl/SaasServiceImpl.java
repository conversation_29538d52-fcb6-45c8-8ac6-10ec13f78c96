package com.hailiang.edu.xsjlqueue.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.hailiang.edu.xsjlqueue.query.UcStudentQuery;
import org.springframework.stereotype.Service;

import com.hailiang.edu.xsjlqueue.dto.saas.req.SaasClassStudentPageQueryReqDto;
import com.hailiang.edu.xsjlqueue.dto.saas.req.SaasSchoolClassQueryReqDto;
import com.hailiang.edu.xsjlqueue.dto.saas.req.SaasTeachManageStaffTeachReqDto;
import com.hailiang.edu.xsjlqueue.dto.saas.req.UnderClassInfoReqDto;
import com.hailiang.edu.xsjlqueue.dto.saas.resp.*;
import com.hailiang.edu.xsjlqueue.remote.biz.saas.SaasClient;
import com.hailiang.edu.xsjlqueue.service.SaasService;

@Service
public class SaasServiceImpl implements SaasService {

    @Resource
    SaasClient saasClient;

    @Override
    public List<TeachManageStaffTeachRespDto> getTeachManageList(Map<Long, Long> userIdWithStaffIdMap,
                                                                 String saasSchoolId) {

        SaasTeachManageStaffTeachReqDto saasTeachManageStaffTeachListReqDto = new SaasTeachManageStaffTeachReqDto();
        saasTeachManageStaffTeachListReqDto.setSchoolId(Long.valueOf(saasSchoolId));
        List<Long> staffIds = new ArrayList<>();
        for (Map.Entry<Long, Long> longLongEntry : userIdWithStaffIdMap.entrySet()) {
            staffIds.add(longLongEntry.getValue());
        }
        saasTeachManageStaffTeachListReqDto.setStaffIds(staffIds);
        return saasClient.getTeachManageStaffTeachListByCondition(saasTeachManageStaffTeachListReqDto);
    }

    @Override
    public List<SaasClassRespDto> getSchoolClassList(SaasSchoolClassQueryReqDto saasSchoolClassQueryReqDto) {
        return saasClient.getSchoolClassList(saasSchoolClassQueryReqDto);
    }

    @Override
    public SaasSchoolRespDto getSchoolInfo(Long schoolId) {
        return saasClient.getSchoolInfo(schoolId);
    }

    @Override
    public List<SaasClassStudentRespDto> queryClassStudentList(SaasClassStudentPageQueryReqDto saasClassStudentPageQueryReqDto) {
        return saasClient.queryClassStudentList(saasClassStudentPageQueryReqDto);
    }

    @Override
    public List<UnderClassInfoRespDto> getUnderClassInfoList(UnderClassInfoReqDto underClassInfoReqDto) {
        return saasClient.getUnderClassInfoList(underClassInfoReqDto);
    }

    @Override
    public List<UcStudentRespDto> queryStudentList(UcStudentQuery query) {
        return saasClient.queryStudentList(query);
    }

    @Override
    public List<SaasClassRespDto> queryClassListByIds(List<Long> classIds) {
        return saasClient.queryClassListByIds(classIds);
    }
}

package com.hailiang.edu.xsjlqueue.convert;


import com.hailiang.edu.xsjlqueue.component.manager.IdManageComponent;
import com.hailiang.edu.xsjlqueue.consts.DeletedConst;
import com.hailiang.edu.xsjlqueue.convert.mapstruct.SmsReportPointStruct;
import com.hailiang.edu.xsjlqueue.dal.entity.XsPointRecord;
import com.hailiang.edu.xsjlqueue.dal.entity.XsSmsReport;
import com.hailiang.edu.xsjlqueue.dal.entity.mongodb.SmsReportPoint;
import com.hailiang.edu.xsjlqueue.dto.sms.RoleDto;
import com.hailiang.edu.xsjlqueue.dto.sms.SubjectDto;
import com.hailiang.edu.xsjlqueue.enums.RoleEnum;
import com.hailiang.edu.xsjlqueue.util.DateUtil;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class SmsReportPointConvert {

    @Resource
    SmsReportPointStruct smsReportPointStruct;
    @Resource
    IdManageComponent idManageComponent;

    public List<SmsReportPoint> toObjList(XsSmsReport xsSmsReport, List<XsPointRecord> xsPointRecordList
            , Map<Long, RoleEnum> userIdWithRoleCodeMap, Map<Long, List<SubjectDto>> userIdWithSubjectListMap) {

        if (CollectionUtils.isEmpty(xsPointRecordList)) {
            return new ArrayList<>();
        }

        List<SmsReportPoint> smsReportPointList = new ArrayList<>();

        for (XsPointRecord xsPointRecord : xsPointRecordList) {

            SmsReportPoint smsReportPoint = smsReportPointStruct.toObj(xsPointRecord);
            smsReportPoint.setSmsReportPointId(idManageComponent.nextId());
            smsReportPoint.setSmsReportId(xsSmsReport.getSmsReportId());
            smsReportPoint.setXsPointRecordId(xsPointRecord.getId());
            smsReportPoint.setGmtCreated(DateUtil.getDateTime());
            smsReportPoint.setGmtCreatedTimeStamp(DateUtil.getCurrentTimeStamp() / 1000);
            smsReportPoint.setGmtModified(DateUtil.getDateTime());
            smsReportPoint.setGmtModifiedTimeStamp(DateUtil.getCurrentTimeStamp() / 1000);
            smsReportPoint.setIsDeleted(DeletedConst.NO);

            smsReportPoint.setRoleList(new ArrayList<>());
            if(!CollectionUtils.isEmpty(userIdWithRoleCodeMap) && userIdWithRoleCodeMap.containsKey(xsPointRecord.getUserId())){
                RoleEnum roleEnum = userIdWithRoleCodeMap.get(xsPointRecord.getUserId());
                List<RoleDto> roleDtoList = new ArrayList<>();
                RoleDto roleDto = new RoleDto();
                roleDto.setRoleCode(roleEnum.getCode());
                roleDto.setRoleName(roleEnum.getName());
                roleDtoList.add(roleDto);
                smsReportPoint.setRoleList(roleDtoList);
            }


            smsReportPoint.setSubjectList(new ArrayList<>());
            if (!CollectionUtils.isEmpty(userIdWithSubjectListMap) && userIdWithSubjectListMap.containsKey(xsPointRecord.getUserId())) {
                smsReportPoint.setSubjectList(userIdWithSubjectListMap.get(xsPointRecord.getUserId()));

            }


            smsReportPointList.add(smsReportPoint);

        }

        return smsReportPointList;
    }
}

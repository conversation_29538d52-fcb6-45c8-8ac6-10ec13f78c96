package com.hailiang.edu.xsjlqueue.convert;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.hailiang.edu.xsjlqueue.component.sls.dto.ReportPointDto;
import com.hailiang.edu.xsjlqueue.convert.mapstruct.FamilyMemberDtoStruct;
import com.hailiang.edu.xsjlqueue.dal.entity.XsSmsReport;
import com.hailiang.edu.xsjlqueue.dto.jxgy.StudentDto;
import com.hailiang.edu.xsjlqueue.dto.saas.resp.UnderClassInfoRespDto;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;


import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class ReportPointDtoConvert {

    @Resource
    FamilyMemberDtoStruct familyMemberDtoStruct;


    public List<ReportPointDto> toList(List<StudentDto> studentDtoList, XsSmsReport xsSmsReport
            , UnderClassInfoRespDto underClassInfoRespDto, String sendMethod) {

        if (CollectionUtils.isEmpty(studentDtoList)) {
            return new ArrayList<>();
        }

        List<ReportPointDto> reportPointDtoList = new ArrayList<>();

        for (StudentDto studentDto : studentDtoList) {

            if (CollectionUtils.isEmpty(studentDto.getFamilyMember())) {
                break;
            }

            ReportPointDto reportPointDto = new ReportPointDto();
            reportPointDto.setSmsReportId(xsSmsReport.getSmsReportId());
            reportPointDto.setSmsReportName(xsSmsReport.getStartTime() + "-" + xsSmsReport.getEndTime());
            reportPointDto.setUserId(xsSmsReport.getUserId());
            reportPointDto.setFamilyStatus(studentDto.getFamilyStatus());
            reportPointDto.setStudentId(Long.valueOf(studentDto.getStudentId()));
            reportPointDto.setStudentName(studentDto.getStudentName());
            reportPointDto.setStudentNo(studentDto.getStudentCode());

            reportPointDto.setSaasClassId(xsSmsReport.getSaasClassId());
            reportPointDto.setSaasClassName(underClassInfoRespDto != null ? underClassInfoRespDto.getClassName() : "");
            reportPointDto.setSaasSchoolId(xsSmsReport.getSaasSchoolId());
            reportPointDto.setSaasSchoolName(underClassInfoRespDto != null ? underClassInfoRespDto.getSchoolName() : "");

            reportPointDto.setSendMethod(sendMethod);
            reportPointDto.setSendTime(xsSmsReport.getSendHour() + ":" + xsSmsReport.getSendMinute());

            reportPointDto.setCreateTime(DateUtil.now());
            reportPointDto.setUpdateTime(DateUtil.now());

            reportPointDto.setFamilyMemberList(familyMemberDtoStruct.toReportObjList(studentDto.getFamilyMember()));

            reportPointDtoList.add(reportPointDto);
        }


        return reportPointDtoList;
    }
}

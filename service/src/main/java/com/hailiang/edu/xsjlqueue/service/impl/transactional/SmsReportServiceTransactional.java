package com.hailiang.edu.xsjlqueue.service.impl.transactional;


import com.hailiang.edu.xsjlqueue.dal.dao.XsSmsReportMapper;
import com.hailiang.edu.xsjlqueue.dal.dao.XsSmsReportStudentMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsSmsReport;
import com.hailiang.edu.xsjlqueue.dal.entity.XsSmsReportStudent;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SmsReportServiceTransactional {


    @Resource
    XsSmsReportMapper xsSmsReportMapper;

    @Resource
    XsSmsReportStudentMapper xsSmsReportStudentMapper;

    @Transactional(rollbackFor = Exception.class)
    public void add(XsSmsReport xsSmsReport, List<XsSmsReportStudent> xsSmsReportStudentList) {

        if (xsSmsReport != null) {
            xsSmsReportMapper.insert(xsSmsReport);
        }

        if (!CollectionUtils.isEmpty(xsSmsReportStudentList)) {
            xsSmsReportStudentMapper.batchAdd(xsSmsReportStudentList);
        }
    }
}

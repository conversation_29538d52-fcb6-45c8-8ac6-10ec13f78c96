package com.hailiang.edu.xsjlqueue.convert.mapstruct;


import com.hailiang.edu.xsjlqueue.convert.mapstruct.strategy.Int2BooleanStrategy;
import com.hailiang.edu.xsjlqueue.convert.mapstruct.strategy.String2LongStrategy;
import com.hailiang.edu.xsjlqueue.dal.entity.XsPointRecord;
import com.hailiang.edu.xsjlqueue.dal.entity.mongodb.SmsReportPoint;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", uses = {Int2BooleanStrategy.class, String2LongStrategy.class})
public interface SmsReportPointStruct {

    SmsReportPoint toObj(XsPointRecord xsPointRecord);
}

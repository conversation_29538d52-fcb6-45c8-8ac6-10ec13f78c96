package com.hailiang.edu.xsjlqueue.convert.mapstruct;


import com.hailiang.edu.xsjlqueue.component.sls.dto.ReportPointDto;
import com.hailiang.edu.xsjlqueue.convert.mapstruct.strategy.Int2BooleanStrategy;
import com.hailiang.edu.xsjlqueue.convert.mapstruct.strategy.String2LongStrategy;
import com.hailiang.edu.xsjlqueue.dto.jxgy.StudentDto;
import com.hailiang.edu.xsjlqueue.dto.jxgy.resp.FamilyStatusResp;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring", uses = {Int2BooleanStrategy.class, String2LongStrategy.class})
public interface FamilyMemberDtoStruct {

    StudentDto.FamilyMemberDto toObj(FamilyStatusResp.FamilyMemberDto familyMemberDto);

    List<StudentDto.FamilyMemberDto> toObjList(List<FamilyStatusResp.FamilyMemberDto> familyMemberDtoList);


    ReportPointDto.FamilyMemberDto toReportObj(StudentDto.FamilyMemberDto familyMemberDto);

    List<ReportPointDto.FamilyMemberDto> toReportObjList(List<StudentDto.FamilyMemberDto> familyMemberDtoList);

}

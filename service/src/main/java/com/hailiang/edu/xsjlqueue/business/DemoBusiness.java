package com.hailiang.edu.xsjlqueue.business;



import com.alibaba.fastjson.JSONObject;
import com.hailiang.edu.xsjlqueue.dto.ResultJson;
import com.hailiang.edu.xsjlqueue.util.LogFormatUtil;
import com.hailiang.edu.xsjlqueue.reqo.DemoReq;
import com.hailiang.edu.xsjlqueue.dto.UserInfo;
import com.hailiang.edu.xsjlqueue.component.jwt.JwtToken;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Log4j2
public class DemoBusiness {

    @Resource
    LogFormatUtil logFormatUtil;

    @Resource
    JwtToken jwtToken;



    public ResultJson index() {
        try {
            //todo 在service层做一些业务逻辑，在这边引入

            return ResultJson.success(new JSONObject());

        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
            return ResultJson.fail(e);
        }
    }

    public ResultJson validate(DemoReq demoReq) {
        try {
            //todo 在service层做一些业务逻辑，在这边引入

            //demo 生成一个登录token
            UserInfo userInfo = new UserInfo();
            userInfo.setUserId(1L);

            String token = jwtToken.encodeUserInfo(userInfo);
            JSONObject result = new JSONObject();
            result.put("token",token);

            return ResultJson.success(result);

        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
            return ResultJson.fail(e);
        }
    }

    public ResultJson validateLogin(DemoReq demoReq,UserInfo userInfo) {
        try {
            //todo 在service层做一些业务逻辑，在这边引入

            return ResultJson.success(new JSONObject());

        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
            return ResultJson.fail(e);
        }
    }

}






/**
 * Hailiang.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.hailiang.edu.xsjlqueue.service.impl;

import cn.hutool.json.JSONUtil;
import com.hailiang.edu.xsjlqueue.util.LogFormatUtil;
import com.hailiang.edu.xsjlqueue.dto.alibaba.AiWriterOutputVO;
import com.hailiang.edu.xsjlqueue.dto.alibaba.AiWriterReq;
import com.hailiang.edu.xsjlqueue.dto.alibaba.AiWriterVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR> Yang
 * @version 智能写作Manager: AiWriterManager.java, v 0.1 2024年09月19日 18:36  Baojiang Yang Exp $
 */
@Slf4j
@Service
public class AiWritingManagerService {

    private WebClient webClient;

    @Value("${aliyun.aimodel.host}")
    private String aiModelUrl;

    @Resource
    LogFormatUtil logFormatUtil;

    @PostConstruct
    public void init() {
        ReactorClientHttpConnector connector = new ReactorClientHttpConnector(HttpClient.create());
        this.webClient = WebClient.builder().baseUrl(aiModelUrl).clientConnector(connector)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, "application/json").build();
    }

    public String completion(AiWriterReq aiWriterReq, String openAppId) {
        logFormatUtil.formatInfo("智能评语入参: openAppId={}, aiWriterReq={}" + openAppId + JSONUtil.toJsonStr(aiWriterReq));
        Flux<AiWriterVO> completion = webClient.post().uri("/ai_gateway/writer/completion")
                .header("Open-App-Id", openAppId).bodyValue(aiWriterReq).retrieve()
                .bodyToFlux(AiWriterVO.class).onErrorResume(WebClientResponseException.class, ex -> {
                    HttpStatus status = ex.getStatusCode();
                    String res = ex.getResponseBodyAsString();
                    logFormatUtil.formatInfo("OpenAI API error: {} {}" + status + res);
                    return Mono.error(new RuntimeException(res));
                });
        // 使用 reduce() 方法将 text 拼接成一个字符串
        Mono<String> concatenatedTextMono = completion
                .map(AiWriterVO::getAiWriterOutputVO) // 提取 AiWritingOutputVO
                .map(AiWriterOutputVO::getText)       // 提取 text
                .reduce((text1, text2) -> text1 + text2); // 拼接字符串
        String block = concatenatedTextMono.block();
        logFormatUtil.formatInfo("智能评语模型返回结果: {}" + block);
        return block;
    }

}
package com.hailiang.edu.xsjlqueue.mq;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.hailiang.edu.xsjlqueue.consts.CommonConst;
import com.hailiang.edu.xsjlqueue.dto.evaluate.EvaluateTaskDto;
import com.hailiang.edu.xsjlqueue.service.EvaluateService;
import com.hailiang.edu.xsjlqueue.util.LogFormatUtil;
import com.hailiang.edu.xsjlqueue.util.Md5Util;
import com.hailiang.edu.xsjlqueue.util.RedisKeyUtil;
import com.hailiang.edu.xsjlqueue.util.RedisUtil;
import com.hailiang.hr.common.hr.threadCache.ReqThreadCache;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.UUID;

@Component
@RocketMQMessageListener(consumerGroup = "${rocketmq.consumer.evaluate.group}", topic = "${rocketmq.evaluate.topic}"
        , consumeThreadMax = 2)
public class EvaluateTaskListener implements RocketMQListener<String> {

    @Resource
    LogFormatUtil logFormatUtil;

    @Resource
    RedisUtil redisUtil;

    @Resource
    EvaluateService evaluateService;

    /**
     * 用来存放taskId，防重复消费时间限制（单位s）
     */
    private static long TASKID_EXPIRATION = 3 * 24 * 60 * 60;

    @Override
    public void onMessage(String message) {
        //设置日志唯一id
        ReqThreadCache.setReqId(Md5Util.stringToMD5(UUID.randomUUID().toString()));

        logFormatUtil.formatInfo("EvaluateTaskListener入参------:" + message);
        if (StrUtil.isEmpty(message)) {
            logFormatUtil.formatInfo("EvaluateTaskListener入参---failed消息参数校验失败------:参数为空");
            return;
        }

        try {
            EvaluateTaskDto evaluateTaskDto = JSONObject.parseObject(message, EvaluateTaskDto.class);
            if (repeatConsume(evaluateTaskDto.getEvaluateTaskId())) {
                return;
            }

            logFormatUtil.formatInfo("开始处理评语生成:" + message);

            evaluateService.doGenEvaluate(evaluateTaskDto);


        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
        }

    }


    /**
     * 判断是否重复消费
     *
     * @param taskId 任务id
     * @return boolean
     */
    private boolean repeatConsume(Long taskId) {
        String key = RedisKeyUtil.getRedisKey(CommonConst.SEND_EVALUATE_MSG_KEY, taskId);
        if (!redisUtil.setIfAbsent(key, "true", TASKID_EXPIRATION)) {
            //增加消费记录
            return true;
        }
        return false;
    }


}

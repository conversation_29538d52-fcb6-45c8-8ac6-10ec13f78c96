package com.hailiang.edu.xsjlqueue.convert;

import com.hailiang.edu.xsjlqueue.component.manager.IdManageComponent;
import com.hailiang.edu.xsjlqueue.consts.DeletedConst;
import com.hailiang.edu.xsjlqueue.dal.entity.XsEvaluateDetail;
import com.hailiang.edu.xsjlqueue.dal.entity.XsEvaluateRecord;
import com.hailiang.edu.xsjlqueue.dal.entity.XsSmsReport;
import com.hailiang.edu.xsjlqueue.dal.entity.mongodb.SmsReportStudentEvaluate;
import com.hailiang.edu.xsjlqueue.util.DateUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class SmsReportEvaluateConvert {

    @Resource
    IdManageComponent idManageComponent;

    public SmsReportStudentEvaluate toReportEvaluate(XsSmsReport xsSmsReport, XsEvaluateRecord evaluateRecord
            , List<XsEvaluateDetail> evaluateDetails) {
        SmsReportStudentEvaluate evaluate = new SmsReportStudentEvaluate();
        evaluate.setSmsReportEvaluateId(idManageComponent.nextId());
        evaluate.setSmsReportId(xsSmsReport.getSmsReportId());
        evaluate.setEvaluateList(convertEvaluate(evaluateRecord, evaluateDetails));

        evaluate.setSaasClassId(evaluateRecord.getSaasClassId());
        evaluate.setSaasSchoolId(evaluateRecord.getSaasSchoolId());
        evaluate.setSaasTenantId(evaluateRecord.getSaasTenantId());
        evaluate.setGmtCreated(DateUtil.getDateTime());
        evaluate.setGmtCreatedTimeStamp(DateUtil.getCurrentTimeStamp() / 1000);
        evaluate.setGmtModified(DateUtil.getDateTime());
        evaluate.setGmtModifiedTimeStamp(DateUtil.getCurrentTimeStamp() / 1000);
        evaluate.setIsDeleted(DeletedConst.NO);
        return evaluate;
    }

    private List<SmsReportStudentEvaluate.EvaluateDto> convertEvaluate(XsEvaluateRecord evaluateRecord, List<XsEvaluateDetail> evaluateDetails) {

        if (CollectionUtils.isEmpty(evaluateDetails)) {
            return new ArrayList<>();
        }
        List<SmsReportStudentEvaluate.EvaluateDto> dtos = new ArrayList<>();
        for (XsEvaluateDetail evaluateDetail : evaluateDetails) {

            SmsReportStudentEvaluate.EvaluateDto evaluate = new SmsReportStudentEvaluate.EvaluateDto();
            evaluate.setEvaluateRecordId(evaluateRecord.getEvaluateRecordId());
            evaluate.setUserId(evaluateRecord.getUserId());
            evaluate.setStudentId(evaluateDetail.getStudentId());
            evaluate.setPlanId(evaluateRecord.getPlanId());
            evaluate.setEvaluateName(evaluateRecord.getEvaluateName());
            evaluate.setEvaluateType(evaluateRecord.getEvaluateType());
            evaluate.setRoleCode(evaluateRecord.getRoleCode());
            evaluate.setUserRoleName(evaluateRecord.getUserRoleName());
            evaluate.setEvaluateDetailId(evaluateDetail.getEvaluateDetailId());
            evaluate.setAskData(evaluateDetail.getAskData());
            evaluate.setEvaluateData(evaluateDetail.getEvaluateData());
            dtos.add(evaluate);
        }
        return dtos;
    }
}

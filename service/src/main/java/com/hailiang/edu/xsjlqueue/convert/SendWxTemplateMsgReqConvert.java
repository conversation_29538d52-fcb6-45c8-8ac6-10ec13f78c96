package com.hailiang.edu.xsjlqueue.convert;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.hailiang.edu.xsjlqueue.dal.entity.XsSmsReport;
import com.hailiang.edu.xsjlqueue.dal.entity.XsUser;
import com.hailiang.edu.xsjlqueue.dto.jxgy.StudentDto;
import com.hailiang.edu.xsjlqueue.dto.jxgy.req.SendWxTemplateMsgReq;
import com.hailiang.edu.xsjlqueue.dto.saas.resp.UnderClassInfoRespDto;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
public class SendWxTemplateMsgReqConvert {


    @Value("${jxgy.location.url}")
    private String locationUrl;

    public SendWxTemplateMsgReq toObj(List<StudentDto> waitWxStudentList, XsSmsReport xsSmsReport
            , XsUser xsUser, String className) {

        SendWxTemplateMsgReq sendWxTemplateMsgReq = new SendWxTemplateMsgReq();
        sendWxTemplateMsgReq.setType("internalDrive");
        sendWxTemplateMsgReq.setFirst("您有一份新的学生积分报告待查收");

        sendWxTemplateMsgReq.setKeyword1(className);

        sendWxTemplateMsgReq.setKeyword2("");
        if (xsUser != null) {
            sendWxTemplateMsgReq.setKeyword2(xsUser.getAccountName() + "老师");
        }

        sendWxTemplateMsgReq.setKeyword3((DateUtil.format(DateUtil.parse(xsSmsReport.getCreateTime()), "yyyy.MM.dd HH:mm")));
        sendWxTemplateMsgReq.setRemark("请家长及时关注学生在校表现");
        sendWxTemplateMsgReq.setUrl(locationUrl + "/h5/report/detail");
        sendWxTemplateMsgReq.setUrlType("1");

        List<SendWxTemplateMsgReq.StudentDto> studentList = new ArrayList<>();
        for (StudentDto studentDto : waitWxStudentList) {
            SendWxTemplateMsgReq.StudentDto dto = new SendWxTemplateMsgReq.StudentDto();
            dto.setStudentCode(studentDto.getStudentCode());
            dto.setStudentName(studentDto.getStudentName());
            dto.setSaasStudentId(studentDto.getStudentId());

            String urlParam = "studentId=" + studentDto.getStudentId() + "&smsReportId=" + xsSmsReport.getSmsReportId();
            dto.setUrlParam(urlParam);

            String content = studentDto.getStudentName() + getSmsReportName(xsSmsReport.getStartTime(), xsSmsReport.getEndTime()) + "的积分表现报告已生成";
            dto.setKeyword4(content);
            studentList.add(dto);
        }

        sendWxTemplateMsgReq.setStudentList(studentList);


        return sendWxTemplateMsgReq;
    }


    private String getSmsReportName(String startTime, String endTime) {

        Date start = DateUtil.parse(startTime, "yyyy-MM-dd");
        Date end = DateUtil.parse(endTime, "yyyy-MM-dd");

        if (DateUtil.compare(start, end) == 0) {
            return DateUtil.format(start, "yyyy-MM-dd");
        } else {
            return DateUtil.format(start, "yyyy-MM-dd") + "至" + DateUtil.format(end, "yyyy-MM-dd");
        }

    }

    public String getRangeTime(String startTime, String endTime) {

        Date start = DateUtil.parse(startTime, "yyyy-MM-dd");
        Date end = DateUtil.parse(endTime, "yyyy-MM-dd");

        //开始日期和结束日期年份一致为M.dd格式 否则为yyyy.MM.dd格式
        int startYear = DateUtil.parse(startTime).year();
        int endYear = DateUtil.parse(endTime).year();
        String formatStr = "M.dd";
        if (startYear != endYear) {
            formatStr = "yyyy.MM.dd";
        }

        if ((DateUtil.compare(start, end) == 0)) {
            return DateUtil.format(start, formatStr);
        } else {
            return DateUtil.format(start, formatStr) + "-" + DateUtil.format(end, formatStr);

        }

    }

    public String getClassname(UnderClassInfoRespDto underClassInfoRespDto) {
        String className = "";
        //优先取班级名称
        if (underClassInfoRespDto != null) {
            className = StrUtil.isEmpty(underClassInfoRespDto.getClassAlias()) ?
                    underClassInfoRespDto.getClassName() : underClassInfoRespDto.getClassAlias();
        }
        return className;
    }

}

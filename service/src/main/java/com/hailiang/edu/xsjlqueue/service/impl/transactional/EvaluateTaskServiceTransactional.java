package com.hailiang.edu.xsjlqueue.service.impl.transactional;


import com.hailiang.edu.xsjlqueue.dal.dao.XsEvaluateDetailMapper;
import com.hailiang.edu.xsjlqueue.dal.dao.XsTaskDetailMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsEvaluateDetail;
import com.hailiang.edu.xsjlqueue.dal.entity.XsTaskDetail;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;

@Service
public class EvaluateTaskServiceTransactional {


    @Resource
    XsTaskDetailMapper xsTaskDetailMapper;

    @Resource
    XsEvaluateDetailMapper xsEvaluateDetailMapper;

    @Transactional(rollbackFor = Exception.class)
    public void update(XsTaskDetail xsTaskDetail, XsEvaluateDetail xsEvaluateDetail) {

        if (xsTaskDetail != null) {
            xsTaskDetailMapper.updateById(xsTaskDetail);
        }

        if (xsEvaluateDetail != null) {
            xsEvaluateDetailMapper.updateById(xsEvaluateDetail);
        }

    }
}

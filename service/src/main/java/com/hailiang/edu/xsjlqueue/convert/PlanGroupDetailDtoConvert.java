package com.hailiang.edu.xsjlqueue.convert;

import cn.hutool.core.collection.CollUtil;
import com.hailiang.edu.xsjlqueue.dal.entity.XsAvatar;
import com.hailiang.edu.xsjlqueue.dal.entity.XsClassStudent;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Log4j2
public class PlanGroupDetailDtoConvert {


    public void overwriteStudentIcon(List<XsClassStudent> xsClassStudentList, List<XsAvatar> studentAvatar) {

        if (!CollUtil.isEmpty(xsClassStudentList) && !CollUtil.isEmpty(studentAvatar)) {
            Map<Long, String> studentIdToUrl = studentAvatar.stream().collect(Collectors.toMap(XsAvatar::getBusinessId,
                    XsAvatar::getFileUrl, (p1, p2) -> p1));

            for (XsClassStudent xsClassStudent : xsClassStudentList) {
                xsClassStudent.setUrl(studentIdToUrl.getOrDefault(xsClassStudent.getStudentId(), xsClassStudent.getUrl()));
            }
        }
    }
}



package com.hailiang.edu.xsjlqueue.service;

import java.util.List;
import java.util.Map;

import com.hailiang.edu.xsjlqueue.dto.saas.req.SaasClassStudentPageQueryReqDto;
import com.hailiang.edu.xsjlqueue.dto.saas.req.SaasSchoolClassQueryReqDto;
import com.hailiang.edu.xsjlqueue.dto.saas.req.UnderClassInfoReqDto;
import com.hailiang.edu.xsjlqueue.dto.saas.resp.*;
import com.hailiang.edu.xsjlqueue.query.UcStudentQuery;

public interface SaasService {

    List<TeachManageStaffTeachRespDto> getTeachManageList(Map<Long, Long> userIdWithStaffIdMap, String saasSchoolId);

    /**
     * 通过学校信息获取学校下班级列表
     * @param saasSchoolClassQueryReqDto
     * @return
     */
    List<SaasClassRespDto> getSchoolClassList(SaasSchoolClassQueryReqDto saasSchoolClassQueryReqDto);

    /**
     * 通过学校ID获取学校信息
     * @param schoolId
     * @return
     */
    SaasSchoolRespDto getSchoolInfo(Long schoolId);

    /**
     * 查询学校下学生信息
     * @param saasClassStudentPageQueryReqDto
     * @return
     */
    List<SaasClassStudentRespDto> queryClassStudentList(SaasClassStudentPageQueryReqDto saasClassStudentPageQueryReqDto);

    /**
     * 查询班级信息
     * @param underClassInfoReqDto
     * @return
     */
    List<UnderClassInfoRespDto> getUnderClassInfoList(UnderClassInfoReqDto underClassInfoReqDto);

    /**
     * 查询学生信息
     * @param query
     * @return
     */
    List<UcStudentRespDto> queryStudentList(UcStudentQuery query);

    /**
     * 通过班级ID获取班级信息
     * @param classIds
     * @return
     */
    List<SaasClassRespDto> queryClassListByIds(List<Long> classIds);
}

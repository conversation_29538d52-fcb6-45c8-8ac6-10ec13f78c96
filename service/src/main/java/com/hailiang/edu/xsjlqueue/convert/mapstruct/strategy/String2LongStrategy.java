package com.hailiang.edu.xsjlqueue.convert.mapstruct.strategy;

import org.springframework.stereotype.Component;

@Component
public class String2LongStrategy {

    public Long stringToLong(String string) {
        return string != null && !string.isEmpty() ? Long.parseLong(string) : null;
    }

    public String long2String(Long longValue) {
        return longValue != null ? String.valueOf(longValue) : null;
    }
}
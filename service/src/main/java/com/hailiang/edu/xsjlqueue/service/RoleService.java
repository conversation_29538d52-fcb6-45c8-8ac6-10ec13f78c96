package com.hailiang.edu.xsjlqueue.service;

import com.hailiang.edu.xsjlqueue.dto.saas.resp.TeachManageStaffTeachRespDto;
import com.hailiang.edu.xsjlqueue.dto.sms.SubjectDto;
import com.hailiang.edu.xsjlqueue.enums.RoleEnum;

import java.util.List;
import java.util.Map;

public interface RoleService {

    /**
     *  获取用户ID 映射 角色code map
     * @param userIdWithStaffIdMap
     * @param saasClassId
     * @param teachManageStaffTeachRespDtos
     * @return
     */
    Map<Long, RoleEnum> getUserIdWithRoleCodeMap(Map<Long, Long> userIdWithStaffIdMap, String saasClassId, List<TeachManageStaffTeachRespDto> teachManageStaffTeachRespDtos);


    /**
     *  获取用户ID 映射 学科列表 map
     *
     * @param userIdWithStaffIdMap
     * @param saasClassId
     * @param teachManageStaffTeachRespDtos
     * @return
     */
    Map<Long, List<SubjectDto>> getUserIdWithSubjectListMap(Map<Long, Long> userIdWithStaffIdMap, String saasClassId, List<TeachManageStaffTeachRespDto> teachManageStaffTeachRespDtos);
}

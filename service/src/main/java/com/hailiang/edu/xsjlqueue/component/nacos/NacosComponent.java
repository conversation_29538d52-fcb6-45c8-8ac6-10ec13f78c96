package com.hailiang.edu.xsjlqueue.component.nacos;


import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.util.IpUtil;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Component
public class NacosComponent {

    /**
     * 登录 nacos 获取 token信息
     */
    private final String loginUrl = "http://%s/nacos/v1/auth/users/login";

    /**
     * 注销 nacos 实例 地址
     */
    private final String cancelInstanceUrl = "http://%s/nacos/v1/ns/instance";

    @Value("${spring.cloud.nacos.discovery.username}")
    private String username;
    @Value("${spring.cloud.nacos.discovery.password}")
    private String password;
    @Value("${spring.cloud.nacos.discovery.server-addr}")
    private String serverAddr;
    @Value("${spring.application.name}")
    private String serverName;
    @Value("${server.port}")
    private String port;
    @Value("${spring.cloud.nacos.discovery.namespace}")
    private String namespaceId;


    @Data
    public static class LoginRespDto {
        private String accessToken;
        private Long tokenTtl;
        private Boolean globalAdmin;
    }

    private String getAccessToken() {
        String url = String.format(loginUrl, serverAddr);
        Map<String, Object> req = new HashMap<>();
        req.put("username", username);
        req.put("password", password);
        String result = HttpUtil.post(url, req);
        if (StrUtil.isEmpty(result)) {
            return "";
        }
        LoginRespDto loginRespDto = JSONObject.parseObject(result, LoginRespDto.class);
        return loginRespDto.getAccessToken();
    }


    public String doCancelInstance() {

        String accessToken = getAccessToken();
        if (StrUtil.isEmpty(accessToken)) {
            return "fail";
        }

        String url = String.format(cancelInstanceUrl, serverAddr);
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("serviceName", serverName);
        paramMap.put("clusterName", "DEFAULT");
        paramMap.put("groupName", "DEFAULT_GROUP");
        paramMap.put("ip", IpUtil.getIp());
        paramMap.put("port", port);
        paramMap.put("namespaceId", namespaceId);
        paramMap.put("accessToken", accessToken);
        String path = URLUtil.buildQuery(paramMap, StandardCharsets.UTF_8);

        String reqUrl = url + "?" + path;
        return HttpRequest.put(reqUrl).execute().body();
    }


}

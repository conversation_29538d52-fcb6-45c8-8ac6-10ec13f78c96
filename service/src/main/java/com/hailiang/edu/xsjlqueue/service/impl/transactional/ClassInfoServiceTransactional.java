package com.hailiang.edu.xsjlqueue.service.impl.transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.hailiang.edu.xsjlqueue.dal.dao.*;
import com.hailiang.edu.xsjlqueue.dal.entity.XsAvatar;
import com.hailiang.edu.xsjlqueue.dal.entity.XsClassStudent;
import com.hailiang.edu.xsjlqueue.dal.entity.XsStudent;
import com.hailiang.edu.xsjlqueue.util.DateUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
public class ClassInfoServiceTransactional {

    @Resource
    XsClassStudentMapper xsClassStudentMapper;
    @Resource
    XsStudentMapper xsStudentMapper;

    @Resource
    XsStudentMasterSlaveMapper xsStudentMasterSlaveMapper;

    @Resource
    XsGroupStudentMapper xsGroupStudentMapper;

    @Resource
    XsPointRecordMapper xsPointRecordMapper;

    @Resource
    XsRedemptionRecordMapper xsRedemptionRecordMapper;

    @Resource
    XsAvatarMapper xsAvatarMapper;

    @Transactional(rollbackFor = Exception.class)
    public void doClassStudentSave(List<XsClassStudent> classStudentList, List<XsStudent> studentList,
                                   List<Long> deleteClassStudentIdList, List<XsClassStudent> deleteClassStudentList,
                                   List<XsStudent> updateStudentList, List<XsAvatar> addAvatarList) {

        if (!CollectionUtils.isEmpty(classStudentList)) {
            this.insertClassStuList(classStudentList, 100);
        }

        if (!CollectionUtils.isEmpty(addAvatarList)) {
            this.insertAvatarList(addAvatarList, 100);
        }

        if (!CollectionUtils.isEmpty(studentList)) {
            this.insertStuList(studentList, 100);
        }

        if (!CollectionUtils.isEmpty(updateStudentList)) {
            this.updateStuList(updateStudentList, 100);
        }

        if (!CollectionUtils.isEmpty(deleteClassStudentIdList)) {
            xsClassStudentMapper.deleteByIds(deleteClassStudentIdList);
        }

        if (!CollectionUtils.isEmpty(deleteClassStudentList)) {
            Map<String, List<Long>> classStudentIds = deleteClassStudentList.stream().collect(Collectors.groupingBy(
                    XsClassStudent::getSaasClassId, Collectors.mapping(XsClassStudent::getStudentId, Collectors.toList())));
            for (Map.Entry<String, List<Long>> stringListEntry : classStudentIds.entrySet()) {

                xsStudentMasterSlaveMapper.delByStudentIds(stringListEntry.getValue(), stringListEntry.getKey());

                xsGroupStudentMapper.delByStudentIds(stringListEntry.getValue(), stringListEntry.getKey());

                xsPointRecordMapper.delByStudentIds(stringListEntry.getValue(), stringListEntry.getKey(),
                        DateUtil.getDateTime());

                xsRedemptionRecordMapper.delByStudentIds(stringListEntry.getValue(), stringListEntry.getKey(),
                        DateUtil.getDateTime());
            }

        }
    }

    private void insertClassStuList(List<XsClassStudent> classStudentList, int size) {
        int insertLength = classStudentList.size();
        int i = 0;
        while (insertLength > size) {
            xsClassStudentMapper.addBatch(classStudentList.subList(i, i + size));
            i = i + size;
            insertLength = insertLength - size;
        }
        if (insertLength > 0) {
            xsClassStudentMapper.addBatch(classStudentList.subList(i, i + insertLength));
        }
    }

    private void insertAvatarList(List<XsAvatar> addAvatarList, int size) {
        int insertLength = addAvatarList.size();
        int i = 0;
        while (insertLength > size) {
            xsAvatarMapper.insertBatch(addAvatarList.subList(i, i + size));
            i = i + size;
            insertLength = insertLength - size;
        }
        if (insertLength > 0) {
            xsAvatarMapper.insertBatch(addAvatarList.subList(i, i + insertLength));
        }
    }

    private void insertStuList(List<XsStudent> studentList, int size) {
        int insertLength = studentList.size();
        int i = 0;
        while (insertLength > size) {
            xsStudentMapper.addBatch(studentList.subList(i, i + size));
            i = i + size;
            insertLength = insertLength - size;
        }
        if (insertLength > 0) {
            xsStudentMapper.addBatch(studentList.subList(i, i + insertLength));
        }
    }

    private void updateStuList(List<XsStudent> studentList, int size) {
        int insertLength = studentList.size();
        int i = 0;
        while (insertLength > size) {
            xsStudentMapper.batchUpdateById(studentList.subList(i, i + size));
            i = i + size;
            insertLength = insertLength - size;
        }
        if (insertLength > 0) {
            xsStudentMapper.batchUpdateById(studentList.subList(i, i + insertLength));
        }
    }
}

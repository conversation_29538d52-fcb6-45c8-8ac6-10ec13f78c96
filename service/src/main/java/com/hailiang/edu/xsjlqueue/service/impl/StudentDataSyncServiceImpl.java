/**
 * Hailiang.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.hailiang.edu.xsjlqueue.service.impl;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import com.hailiang.edu.xsjlqueue.component.manager.IdManageComponent;
import com.hailiang.edu.xsjlqueue.consts.AvatarBusinessTypeConst;
import com.hailiang.edu.xsjlqueue.consts.AvatarDataTypeConst;
import com.hailiang.edu.xsjlqueue.dal.dao.XsAvatarMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsAvatar;
import com.hailiang.edu.xsjlqueue.query.XsAvatarQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.hailiang.edu.xsjlqueue.component.ding.DingTalkComponent;
import com.hailiang.edu.xsjlqueue.consts.CommonConst;
import com.hailiang.edu.xsjlqueue.dal.biz.StudentIconStorage;
import com.hailiang.edu.xsjlqueue.dal.biz.StudentSyncStorage;
import com.hailiang.edu.xsjlqueue.dal.dao.XsClassStudentMapper;
import com.hailiang.edu.xsjlqueue.dal.dao.XsStudentMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsClassStudent;
import com.hailiang.edu.xsjlqueue.dal.entity.XsStudent;
import com.hailiang.edu.xsjlqueue.dto.saas.req.SaasClassStudentPageQueryReqDto;
import com.hailiang.edu.xsjlqueue.dto.saas.req.SaasSchoolClassQueryReqDto;
import com.hailiang.edu.xsjlqueue.dto.saas.req.UnderClassInfoReqDto;
import com.hailiang.edu.xsjlqueue.dto.saas.resp.*;
import com.hailiang.edu.xsjlqueue.dto.stu.StudentDataSyncTaskDto;
import com.hailiang.edu.xsjlqueue.query.ClassStudentQuery;
import com.hailiang.edu.xsjlqueue.query.StudentQuery;
import com.hailiang.edu.xsjlqueue.query.UcStudentQuery;
import com.hailiang.edu.xsjlqueue.service.SaasService;
import com.hailiang.edu.xsjlqueue.service.StudentDataSyncService;
import com.hailiang.edu.xsjlqueue.service.impl.transactional.ClassInfoServiceTransactional;
import com.hailiang.edu.xsjlqueue.util.LogFormatUtil;
import com.hailiang.edu.xsjlqueue.util.Md5Util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;

/**
 * <AUTHOR>
 * @version v0.1: StudentDataSyncServiceImpl.java, v 0.1 2023年09月19日 19:32  zhousx Exp $
 */
@Service
public class StudentDataSyncServiceImpl implements StudentDataSyncService {

    @Resource
    private LogFormatUtil logFormatUtil;

    @Resource
    private SaasService saasService;

    @Resource
    private XsClassStudentMapper xsClassStudentMapper;

    @Resource
    private XsStudentMapper xsStudentMapper;

    @Resource
    private StudentIconStorage studentIconStorage;

    @Resource
    private ClassInfoServiceTransactional classInfoServiceTransactional;

    @Resource
    private DingTalkComponent dingTalkComponent;

    @Resource
    private StudentSyncStorage studentSyncStorage;

    @Resource(name = "businessExecutor")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource
    IdManageComponent idManageComponent;
    @Resource
    XsAvatarMapper xsAvatarMapper;

    @Value("${spring.profiles.active}")
    private String env;

    private final String stuSyncSchoolKey = "stuSyncSchool";
    private final String stuSyncDataKey = "stuSyncData";

    private final String stuSyncCountKey = "stuSyncCount";

    @Override
    public void doStuSync(StudentDataSyncTaskDto taskDto) {
        if (Objects.isNull(taskDto) || CollectionUtils.isEmpty(taskDto.getClassIds())) {
            return;
        }
        logFormatUtil.formatInfo("学生数据比对入参:" + JSONObject.toJSONString(taskDto));

        // 1.获取saas学校+学生数据
        List<SaasClassStuRespDto> saasSchoolStuRespDtos = this.querySaasStudent(taskDto.getClassIds());

        logFormatUtil.formatInfo("saasSchoolStuRespDtos:" + JSONObject.toJSONString(taskDto));
        if (CollectionUtils.isEmpty(saasSchoolStuRespDtos)) {
            // 2.判断此时是否是最后一批，如果是则发推送
            doSendDingMsg(taskDto);
            return;
        }

        // 3.获取saas班级下学生信息
        List<SaasClassStudentRespDto> saasClassStudentRespDtos = saasSchoolStuRespDtos.stream()
                .filter(o -> CollectionUtils.isNotEmpty(o.getClassStuList()))
                .flatMap(list -> list.getClassStuList().stream()).collect(Collectors.toList());
        // 4.获取学校信息&获取saas的班级信息
        List<SaasSchoolRespDto> saasSchoolList = Lists.newArrayList();
        List<SaasClassRespDto> saasClassRespDtos = Lists.newArrayList();
        saasSchoolStuRespDtos.forEach(element -> {
            saasSchoolList.add(element.getSaasSchoolRespDto());
            saasClassRespDtos.add(element.getSaasClassRespDto());
        });
        List<String> saasClassIds = taskDto.getClassIds().stream().map(String::valueOf).collect(Collectors.toList());
        // 5.获取星动力校级别班级学生数据
        ClassStudentQuery classStudentQuery = new ClassStudentQuery();
        classStudentQuery.setSaasClassIds(saasClassIds);
        List<XsClassStudent> classStudentList = xsClassStudentMapper.getListByCondition(classStudentQuery);
        // 6.获取星动力学生数据
        List<XsStudent> studentList = new ArrayList<>();
        List<UcStudentRespDto> saasStudentList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(saasClassStudentRespDtos)) {
            Set<Long> studentIdSet = saasClassStudentRespDtos.stream().map(SaasClassStudentRespDto::getStudentId)
                    .collect(Collectors.toSet());
            StudentQuery studentQuery = new StudentQuery();
            studentQuery.setStudentIds(studentIdSet);
            studentList = xsStudentMapper.getListByCondition(studentQuery);

            // 7.获取saas的学生ID，查询学生信息
            UcStudentQuery ucStudentQuery = new UcStudentQuery();
            ucStudentQuery.setPageSize(Integer.MAX_VALUE);
            ucStudentQuery.setIsDeleted(Boolean.FALSE);
            ucStudentQuery.setIds(new ArrayList<>(studentIdSet));
            saasStudentList = saasService.queryStudentList(ucStudentQuery);
        }
        // 8.进行同步操作
        this.doSyncClassStuList(taskDto, saasClassStudentRespDtos, classStudentList, studentList, saasClassIds,
                saasSchoolList, saasStudentList);
    }

    /**
     * 获取saas中学校下的所有学生班级信息
     *
     * @param classIds
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    private List<SaasClassStuRespDto> querySaasStudent(List<Long> classIds) {
        if (CollectionUtils.isEmpty(classIds)) {
            return Lists.newArrayList();
        }
        int classLen = classIds.size();

        logFormatUtil.formatInfo("classLen:" + classLen);

        List<Future> allFuture = new ArrayList<>();
        for (int i = 0; i < classLen; i++) {
            int finalI = i;
            Future<List<SaasClassStuRespDto>> future = threadPoolTaskExecutor.submit(() -> {
                Long classId = classIds.get(finalI);
                List<SaasClassStuRespDto> result = new ArrayList<>();

                logFormatUtil.formatInfo("Do ClassId:" + classId);

                // 2.查询班级信息
                List<SaasClassRespDto> saasClassRespDtos = saasService.queryClassListByIds(Arrays.asList(classId));
                if (CollectionUtils.size(saasClassRespDtos) != 1) {
                    logFormatUtil.formatInfo("班级信息错误，classId:" + classId);
                    return result;
                }
                SaasClassStuRespDto saasSchoolStuRespDto = new SaasClassStuRespDto();
                result.add(saasSchoolStuRespDto);
                saasSchoolStuRespDto.setSaasClassRespDto(saasClassRespDtos.iterator().next());
                Long schoolId = saasSchoolStuRespDto.getSaasClassRespDto().getSchoolId();
                // 3.查询班级所属学校信息
                SaasSchoolRespDto saasSchoolRespDto = saasService.getSchoolInfo(schoolId);
                if (saasSchoolRespDto == null) {
                    logFormatUtil.formatInfo("学校不存在，schoolId:" + schoolId);
                    return result;
                }
                saasSchoolStuRespDto.setSaasSchoolRespDto(saasSchoolRespDto);

                // 4.查询saas中班级下的学生数据
                SaasClassStudentPageQueryReqDto saasReqDto = new SaasClassStudentPageQueryReqDto();
                saasReqDto.setPageSize(CommonConst.PAGE_SIZE);
                saasReqDto.setClassId(classId);
                saasReqDto.setSchoolId(schoolId);
                saasReqDto.setStudentStatus(CommonConst.STUDENT_STATUS);
                saasReqDto.setUpgradeStatus(CommonConst.UPGRADE_STATUS);
                List<SaasClassStudentRespDto> saasClassStudentRespDtoList = saasService
                        .queryClassStudentList(saasReqDto);
                if (CollectionUtils.isEmpty(saasClassStudentRespDtoList)) {
                    logFormatUtil.formatInfo("班级下不存在学生，classId:" + classId);
                    return result;
                }
                saasClassStudentRespDtoList.forEach(e -> {
                    e.setSchoolId(schoolId);
                });
                saasSchoolStuRespDto.setClassStuList(saasClassStudentRespDtoList);
                return result;
            });
            allFuture.add(future);
        }

        List<SaasClassStuRespDto> last = new ArrayList<>();
        logFormatUtil.formatInfo("do feature get");
        try {
            for (Future future : allFuture) {
                List<SaasClassStuRespDto> stuRespDtos = (List<SaasClassStuRespDto>) future.get();
                last.addAll(stuRespDtos);
            }
        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
        }
        logFormatUtil.formatInfo("lastSize:" + last.size());
        return last;
    }

    /**
     * 同步学生数据
     *
     * @param taskDto
     * @param saasClassStudentRespDtoList
     * @param classStudentList
     * @param studentList
     * @param saasClassIds
     * @param saasSchoolList
     */
    private void doSyncClassStuList(StudentDataSyncTaskDto taskDto,
                                    List<SaasClassStudentRespDto> saasClassStudentRespDtoList,
                                    List<XsClassStudent> classStudentList, List<XsStudent> studentList,
                                    List<String> saasClassIds, List<SaasSchoolRespDto> saasSchoolList,
                                    List<UcStudentRespDto> saasStudentList) {
        // 1.获取需要新增的班级学生关系数据
        List<SaasClassStudentRespDto> addClassStudentList = Lists.newArrayList();
        // 2.获取需要删除的班级学生关系数据ids
        List<Long> deleteClassStudentIdList = Lists.newArrayList();
        List<XsClassStudent> deleteClassStudentList = Lists.newArrayList();
        // 3.获取需要更新的学生信息
        List<XsStudent> updateStudentList = Lists.newArrayList();
        // 3.处理同步数据，判断哪些需要删除、哪些需要新增的
        this.handleClassStudent(addClassStudentList, deleteClassStudentIdList, saasClassStudentRespDtoList,
                classStudentList, saasClassIds, deleteClassStudentList, studentList, saasStudentList, updateStudentList);
        // 4.获取原先的学生ids
        List<Long> studentIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(studentList)) {
            studentIds = studentList.stream().map(XsStudent::getStudentId).collect(Collectors.toList());
        }
        Map<Long, UcStudentRespDto> saasStudentMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(saasStudentList)) {
            saasStudentMap = saasStudentList.stream()
                .collect(Collectors.toMap(UcStudentRespDto::getId, Function.identity(), (v1, v2) -> v2));
        }

        // 5.<学校ID，学校信息>映射关系
        Map<Long, SaasSchoolRespDto> saasSchoolMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(saasSchoolList)) {
            saasSchoolList = saasSchoolList.stream().distinct().collect(Collectors.toList());
            saasSchoolMap = saasSchoolList.stream()
                    .collect(Collectors.toMap(SaasSchoolRespDto::getId, Function.identity(), (p1, p2) -> p1));
        }

        List<XsClassStudent> addTchClassStudentList = new ArrayList<>();
        List<XsStudent> addTchStudentList = new ArrayList<>();
        List<XsAvatar> addAvatarList = new ArrayList<>();
        // 5.组装新增数据
        String currentTime = DateUtil.now();
        if (CollectionUtils.isNotEmpty(addClassStudentList)) {
            addClassStudentList = addClassStudentList.stream().filter(o -> Objects.nonNull(o.getStudentNo()))
                    .collect(Collectors.toList());
            for (SaasClassStudentRespDto element : addClassStudentList) {
                SaasSchoolRespDto saasSchoolRespDto = saasSchoolMap.get(element.getSchoolId());
                if (Objects.isNull(saasSchoolRespDto)) {
                    continue;
                }
                // 6.班级关系
                XsClassStudent xsClassStudent = new XsClassStudent();
                xsClassStudent.setSaasClassId(String.valueOf(element.getClassId()));
                xsClassStudent.setStudentId(element.getStudentId());
                xsClassStudent.setUrl("");
                xsClassStudent.setSaasSchoolId(String.valueOf(element.getSchoolId()));
                xsClassStudent.setSaasTenantId(String.valueOf(saasSchoolRespDto.getTenantId()));
                xsClassStudent.setCreateTime(currentTime);
                xsClassStudent.setUpdateTime(currentTime);
                addTchClassStudentList.add(xsClassStudent);
                if (!studentIds.contains(element.getStudentId())) {
                    XsStudent xsStudent = new XsStudent();
                    // 7.学生信息
                    xsStudent.setStudentId(element.getStudentId());
                    xsStudent.setStudentNo(element.getStudentNo());
                    xsStudent.setStudentName(element.getStudentName());
                    xsStudent.setPassword(Md5Util.stringToMD5(CommonConst.DEFAULT_PWD));
                    xsStudent.setIsLeader(0);
                    xsStudent.setIsExpire(0);
                    xsStudent.setIsSuspend(0);
                    UcStudentRespDto ucStudentRespDto = saasStudentMap.get(element.getStudentId());
                    if (Objects.isNull(ucStudentRespDto)) {
                        continue;
                    }
                    xsStudent.setSaasSchoolId(String.valueOf(ucStudentRespDto.getSchoolId()));
                    xsStudent.setSaasTenantId(String.valueOf(saasSchoolRespDto.getTenantId()));
                    xsStudent.setCreateTime(currentTime);
                    xsStudent.setUpdateTime(currentTime);

                    addTchStudentList.add(xsStudent);
                }
            }

            Map<String, List<XsAvatar>> currentClassToStudentAvatarMap = new HashMap<>(16);
            Map<String, List<XsClassStudent>> newClassToStudentMap = new HashMap<>(16);
            if(!CollUtil.isEmpty(addTchClassStudentList)){

                Set<String> classIds = addTchClassStudentList.stream().map(XsClassStudent::getSaasClassId).collect(Collectors.toSet());
                //新增数据中 已有学生班级头像数据
                XsAvatarQuery xsAvatarQuery = new XsAvatarQuery();
                xsAvatarQuery.setSaasClassIds(classIds);
                xsAvatarQuery.setBusinessType(AvatarBusinessTypeConst.STUDENT);
                List<XsAvatar> currentStudentAvatarList = xsAvatarMapper.getListByCondition(xsAvatarQuery);

                if (!CollUtil.isEmpty(currentStudentAvatarList)) {
                    currentClassToStudentAvatarMap = currentStudentAvatarList.stream().
                            collect(Collectors.groupingBy(XsAvatar::getSaasClassId));
                }

                newClassToStudentMap = addTchClassStudentList.stream()
                        .collect(Collectors.groupingBy(XsClassStudent::getSaasClassId));

                for (String classId : classIds) {
                    List<XsClassStudent> newClassStudents = newClassToStudentMap.get(classId);
                    //班级已存在的学生头像
                    List<XsAvatar> oldClassAvatar = currentClassToStudentAvatarMap.get(classId);
                    List<Long> oldStudentIds = new ArrayList<>();

                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(oldClassAvatar)) {
                        oldStudentIds = oldClassAvatar.stream().map(XsAvatar::getBusinessId).distinct()
                                .collect(Collectors.toList());
                    }
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(newClassStudents)) {
                        for (XsClassStudent element : newClassStudents) {
                            if (!oldStudentIds.contains(element.getStudentId())) {
                                //班级学生头像不存在 需要新增
                                XsAvatar xsAvatar = new XsAvatar();
                                xsAvatar.init();
                                xsAvatar.setAvatarId(idManageComponent.nextId());
                                xsAvatar.setUserId(0L);
                                xsAvatar.setFileUrl(studentIconStorage.getStudentRandomIcon(String.valueOf(element.getSaasClassId())));
                                xsAvatar.setBusinessId(element.getStudentId());
                                xsAvatar.setBusinessType(AvatarBusinessTypeConst.STUDENT);
                                xsAvatar.setDataType(AvatarDataTypeConst.SYSTEM);
                                xsAvatar.setSysIconType(CommonConst.ICON_TYPE_MONSTER);
                                xsAvatar.setSaasClassId(String.valueOf(element.getSaasClassId()));
                                xsAvatar.setSaasSchoolId(String.valueOf(element.getSaasSchoolId()));
                                xsAvatar.setSaasTenantId(String.valueOf(element.getSaasTenantId()));
                                addAvatarList.add(xsAvatar);
                            }
                        }
                    }
                }
            }
        }
        // 8.数据处理，批量入库
        classInfoServiceTransactional.doClassStudentSave(addTchClassStudentList, addTchStudentList,
                deleteClassStudentIdList, deleteClassStudentList, updateStudentList,addAvatarList);

        List<XsClassStudent> syncList = Lists.newArrayList();
        syncList.addAll(addTchClassStudentList);
        syncList.addAll(deleteClassStudentList);

        // 9.将本次执行结果放入redis
        studentSyncStorage.setStuSyncData(taskDto, syncList, saasSchoolList);

        //发信息
        doSendDingMsg(taskDto);
    }

    private void doSendDingMsg(StudentDataSyncTaskDto taskDto) {
        Object consumedCount = studentSyncStorage.getStuSyncData(taskDto, stuSyncCountKey);
        if (consumedCount == null) {
            return;
        }
        logFormatUtil.formatInfo("allMsgCount" + taskDto.getMsgCount() + "- allConsumedCount" + (int) consumedCount);

        if (NumberUtil.compare((int) consumedCount, taskDto.getMsgCount()) >= 0) {
            // 12.发送钉钉卡片消息
            String text = this.getMarkDownText(taskDto);
            dingTalkComponent.send(text);
            // 13.删除本批次消息redis内容
            studentSyncStorage.clearStuSyncInfo(taskDto);
        }
    }

    /**
     * 处理学生数据,识别需要新增、删除的数据
     *
     * @param addClassStudentList
     * @param deleteClassStudentIdList
     * @param saasClassStudentRespDtoList
     * @param classStudentList
     * @param saasClassIds
     * @param deleteClassStudentList
     */
    private void handleClassStudent(List<SaasClassStudentRespDto> addClassStudentList,
                                    List<Long> deleteClassStudentIdList,
                                    List<SaasClassStudentRespDto> saasClassStudentRespDtoList,
                                    List<XsClassStudent> classStudentList, List<String> saasClassIds,
                                    List<XsClassStudent> deleteClassStudentList, List<XsStudent> studentList,
                                    List<UcStudentRespDto> saasStudentList, List<XsStudent> updateStudentList) {
        // 1.获取最新的学生班级数据，通过班级id分组
        Map<Long, List<SaasClassStudentRespDto>> newClassStudentMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(saasClassStudentRespDtoList)) {
            newClassStudentMap = saasClassStudentRespDtoList.stream()
                    .collect(Collectors.groupingBy(SaasClassStudentRespDto::getClassId));
        }
        // 2.原先的学生班级数据，通过班级id分组
        Map<String, List<XsClassStudent>> oldClassStudentMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(classStudentList)) {
            oldClassStudentMap = classStudentList.stream()
                    .collect(Collectors.groupingBy(XsClassStudent::getSaasClassId));
        }

        for (String saasClassId : saasClassIds) {
            // 3.新班级学生数据
            List<SaasClassStudentRespDto> newClassStudentList = newClassStudentMap.get(Long.valueOf(saasClassId));
            List<Long> newStudentIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(newClassStudentList)) {
                newStudentIds = newClassStudentList.stream().map(SaasClassStudentRespDto::getStudentId).distinct()
                        .collect(Collectors.toList());
            }
            // 4.老班级学生数据
            List<XsClassStudent> oldClassStudentList = oldClassStudentMap.get(saasClassId);
            List<Long> oldStudentIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(oldClassStudentList)) {
                oldStudentIds = oldClassStudentList.stream().map(XsClassStudent::getStudentId).distinct()
                        .collect(Collectors.toList());
                for (XsClassStudent element : oldClassStudentList) {
                    if (!newStudentIds.contains(element.getStudentId())) {
                        // 5.新班级学生不存在需要删除
                        deleteClassStudentIdList.add(element.getId());
                        deleteClassStudentList.add(element);
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(newClassStudentList)) {
                for (SaasClassStudentRespDto element : newClassStudentList) {
                    if (!oldStudentIds.contains(element.getStudentId())) {
                        // 6.老班级学生不存在需要新增
                        addClassStudentList.add(element);
                    }
                }
            }

        }

        logFormatUtil.formatInfo("学生主体数据fromSaas:" + JSONObject.toJSONString(saasStudentList));
        if (CollectionUtils.isEmpty(saasStudentList)) {
            return;
        }
        // 7.判断学生主体信息是否发生变化
        Map<Long, UcStudentRespDto> saasStudentMap = saasStudentList.stream()
                .collect(Collectors.toMap(UcStudentRespDto::getId, Function.identity()));
        for (XsStudent xsStudent : studentList) {
            UcStudentRespDto ucStudentRespDto = saasStudentMap.get(xsStudent.getStudentId());
            if (Objects.isNull(ucStudentRespDto)) {
                continue;
            }
            if (ucStudentRespDto.getStudentNo().equals(xsStudent.getStudentNo())
                    && ucStudentRespDto.getName().equals(xsStudent.getStudentName())
                    && CommonConst.IS_DELETED_NO.equals(xsStudent.getIsSuspend())) {
                continue;
            }
            if (CommonConst.IS_DELETED_YES.equals(xsStudent.getIsSuspend())) {
                xsStudent.setIsSuspend(CommonConst.IS_DELETED_NO);
            }
            xsStudent.setStudentNo(ucStudentRespDto.getStudentNo());
            xsStudent.setStudentName(ucStudentRespDto.getName());
            xsStudent.setUpdateTime(DateUtil.now());
            updateStudentList.add(xsStudent);
        }

    }

    /**
     * 组装钉钉卡片消息内容
     *
     * @param taskDto
     * @return
     */
    @Async
    public String getMarkDownText(StudentDataSyncTaskDto taskDto) {
        StringBuilder text = new StringBuilder(
                "## 【" + env + "】" + DateUtil.format(DateUtil.yesterday(), "yyyy-MM-dd") + "学生数据同步 \n");
        text.append("> ![screenshot](https://static-inspire-stu.hailiangedu.com/system/stat.jpeg)\n");

        Object stuSyncObj = studentSyncStorage.getStuSyncData(taskDto, stuSyncDataKey);
        List<StuSyncResultRespDto> syncClassStuList = Objects.isNull(stuSyncObj) ? Lists.newArrayList()
                : JSONObject.parseArray((String) stuSyncObj, StuSyncResultRespDto.class);

        text.append("### 数据同步情况 \n\n");

        if (CollectionUtils.isEmpty(syncClassStuList)) {
            text.append("> 数据无差异\n\n");
            return text.toString();
        }
        // 6.拼接卡片信息
        for (StuSyncResultRespDto stuSync : syncClassStuList) {
            if (stuSync.getSyncClassCount() > 0) {
                text.append("> " + stuSync.getSchoolName() + "【" + stuSync.getSchoolId() + "】:更新【"
                        + stuSync.getSyncClassCount() + "】个班级 \n\n");
            }
        }
        return text.toString();
    }

}
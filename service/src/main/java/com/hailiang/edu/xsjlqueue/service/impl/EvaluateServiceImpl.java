package com.hailiang.edu.xsjlqueue.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.hailiang.edu.xsjlqueue.consts.*;
import com.hailiang.edu.xsjlqueue.convert.EvaluateAskDataConvert;
import com.hailiang.edu.xsjlqueue.dal.dao.*;
import com.hailiang.edu.xsjlqueue.dal.entity.*;
import com.hailiang.edu.xsjlqueue.dto.alibaba.AiWriterReq;
import com.hailiang.edu.xsjlqueue.dto.evaluate.EvaluateTaskDto;
import com.hailiang.edu.xsjlqueue.dto.saas.req.UnderClassInfoReqDto;
import com.hailiang.edu.xsjlqueue.dto.saas.resp.UnderClassInfoRespDto;
import com.hailiang.edu.xsjlqueue.enums.RoleEnum;
import com.hailiang.edu.xsjlqueue.enums.SaasSectionEnum;
import com.hailiang.edu.xsjlqueue.exception.BusinessException;
import com.hailiang.edu.xsjlqueue.query.*;
import com.hailiang.edu.xsjlqueue.remote.biz.baidu.BaiduAiClient;
import com.hailiang.edu.xsjlqueue.remote.biz.saas.SaasClient;
import com.hailiang.edu.xsjlqueue.service.EvaluateService;
import com.hailiang.edu.xsjlqueue.service.impl.transactional.EvaluateTaskServiceTransactional;
import com.hailiang.edu.xsjlqueue.util.LogFormatUtil;
import jodd.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class EvaluateServiceImpl implements EvaluateService {

    @Resource
    XsEvaluateRecordMapper xsEvaluateRecordMapper;
    @Resource
    BaiduAiClient baiduAiClient;
    @Resource
    XsTaskMapper xsTaskMapper;
    @Resource
    XsTaskDetailMapper xsTaskDetailMapper;
    @Resource
    XsEvaluateDetailMapper xsEvaluateDetailMapper;
    @Resource
    LogFormatUtil logFormatUtil;
    @Resource
    EvaluateTaskServiceTransactional evaluateTaskServiceTransactional;
    @Resource
    SaasClient saasClient;
    @Resource
    XsStudentMapper xsStudentMapper;
    @Resource
    EvaluateAskDataConvert evaluateAskDataConvert;
    @Resource
    XsPointRecordMapper xsPointRecordMapper;
    @Resource
    AiWritingManagerService aiWritingManagerService;

    @Value("${saas.appId}")
    private String appId;

    @Value("${aliyun.aimodel.appId}")
    private String aiModelAppId;

    @Override
    public void doGenEvaluate(EvaluateTaskDto evaluateTaskDto) throws BusinessException {

        //查询当前评语记录id的状态
        EvaluateRecordQuery evaluateRecordQuery = new EvaluateRecordQuery();
        evaluateRecordQuery.setEvaluateRecordId(String.valueOf(evaluateTaskDto.getEvaluateRecordId()));

        XsEvaluateRecord xsEvaluateRecord = xsEvaluateRecordMapper.getRowByCondition(evaluateRecordQuery);
        //评语记录不存在 或者评语记录不是初始状态 则不生成
        if (null == xsEvaluateRecord || !xsEvaluateRecord.getEvaluateStatus().equals(EvaluateStatusConst.UN_SAVE)) {
            logFormatUtil.formatInfo("评语记录状态异常----退出------:" + evaluateTaskDto.getEvaluateRecordId());
            return;
        }

        //获取任务详情
        TaskQuery taskQuery = new TaskQuery();
        taskQuery.setBusinessId(String.valueOf(xsEvaluateRecord.getEvaluateRecordId()));
        taskQuery.setBusinessType(BusinessTypeConst.EVALUATE);
        XsTask rowByCondition = xsTaskMapper.getRowByCondition(taskQuery);

        //不存在任务
        if (rowByCondition == null) {

            logFormatUtil.formatInfo("任务状态异常----退出------:");
            return;
        }

        TaskDetailQuery taskDetailQuery = new TaskDetailQuery();
        taskDetailQuery.setTaskId(String.valueOf(rowByCondition.getTaskId()));
        taskDetailQuery.setAssigneeId(String.valueOf(evaluateTaskDto.getStudentId()));
        taskDetailQuery.setAssigneeType(AssigneeTypeConst.STUDENT);
        taskDetailQuery.setStatus(TaskDetailStatusConst.NO_EXECUTE);
        XsTaskDetail taskDetail = xsTaskDetailMapper.getRowByCondition(taskDetailQuery);

        EvaluateDetailQuery evaluateDetailQuery = new EvaluateDetailQuery();
        evaluateDetailQuery.setEvaluateRecordId(String.valueOf(xsEvaluateRecord.getEvaluateRecordId()));
        evaluateDetailQuery.setSaasClassId(xsEvaluateRecord.getSaasClassId());
        evaluateDetailQuery.setStudentId(String.valueOf(evaluateTaskDto.getStudentId()));

        XsEvaluateDetail xsEvaluateDetail = xsEvaluateDetailMapper.getRowByCondition(evaluateDetailQuery);

        //存在任务详情
        if (taskDetail == null || xsEvaluateDetail == null) {
            logFormatUtil.formatInfo("任务详情或者评语详情异常---退出------:");
            return;
        }


        String askData = "";
        String studentEvaluateData;
        //如果当前是体验班 取模版评语数据
        if (evaluateTaskDto.getStudentId() < 0L) {
            studentEvaluateData = defaultEvaluate(String.valueOf(evaluateTaskDto.getStudentId()));
        } else {
            //拼装咒语
            askData = getAskData(evaluateTaskDto, xsEvaluateRecord, xsEvaluateDetail.getStudentName());

            //百度生成评语
            studentEvaluateData = baiduAiClient.getStudentEvaluateData(askData);

//            AiWriterReq aiWriterReq = new AiWriterReq();
//            aiWriterReq.setUserPrompt(askData);
//            aiWriterReq.setSessionId("");
//            aiWriterReq.setAppId(aiModelAppId);
//            //阿里模型生成评语
//            studentEvaluateData = aiWritingManagerService.completion(aiWriterReq, appId);
        }

        //更新评语与任务详情
        xsEvaluateDetail.setAskData(askData);
        xsEvaluateDetail.setEvaluateData(studentEvaluateData);

        //任务状态 默认成功
        taskDetail.setStatus(TaskDetailStatusConst.EXECUTED);

        if (StringUtil.isEmpty(studentEvaluateData)) {
            //空字符串  则代表生成失败
            taskDetail.setStatus(TaskDetailStatusConst.FAIL);
        }

        evaluateTaskServiceTransactional.update(taskDetail, xsEvaluateDetail);
    }

    private String getAskData(EvaluateTaskDto evaluateTaskDto, XsEvaluateRecord xsEvaluateRecord, String studentName) {
        String userRoleName = "老师";
        String term = xsEvaluateRecord.getEvaluateType().equals(EvaluateTypeConst.DAILY) ? "最近" : "本学期";
        String termName = xsEvaluateRecord.getEvaluateType().equals(EvaluateTypeConst.DAILY) ? "日常" : "期末";
        //加分明细
        List<String> plusComment = new ArrayList<>();
        //减分明细
        List<String> minusComment = new ArrayList<>();
        //性格标签明细
        List<String> characterTagList = new ArrayList<>();
//        if (evaluateReq.getPersonalityTagList() != null && !CollUtil.isEmpty(evaluateReq.getPersonalityTagList())) {
//            characterTagList = evaluateReq.getPersonalityTagList();
//        }
        //学段code
        String sectionCode = "";

        //根据班级id获取班级信息
        UnderClassInfoReqDto underClassInfoReqDto = new UnderClassInfoReqDto();
        List<Long> classIdList = new ArrayList<>();
        classIdList.add(Long.valueOf(xsEvaluateRecord.getSaasClassId()));
        underClassInfoReqDto.setClassIdList(classIdList);

        List<UnderClassInfoRespDto> classDetailByIds = saasClient.getUnderClassInfoList(underClassInfoReqDto);

        if (!CollUtil.isEmpty(classDetailByIds)) {
            sectionCode = classDetailByIds.get(0).getSectionCode();
            userRoleName = SaasSectionEnum.getNameByCode(sectionCode) + classDetailByIds.get(0).getGradeName() + xsEvaluateRecord.getUserRoleName();
        }

        //数据日期范围
        String startTime = DateUtil.beginOfDay(DateUtil.parse(xsEvaluateRecord.getStartTime())).toString();
        String endTime = DateUtil.endOfDay(DateUtil.parse(xsEvaluateRecord.getEndTime())).toString();
        //积分兑换对数据去除 查询全班的数据
        List<Long> exceptChannelIds = new ArrayList<>();
        exceptChannelIds.add((long) XsPointRecord.CHANNEL_PRIZE);

        PointRecordQuery pointRecordQuery = new PointRecordQuery();
        pointRecordQuery.setSaasClassId(xsEvaluateRecord.getSaasClassId());
        pointRecordQuery.setStudentId(evaluateTaskDto.getStudentId());
        //如果不是班主任 仅取自己点评
        if (!xsEvaluateRecord.getRoleCode().equals(RoleEnum.HEAD_TEACHER.getCode())) {
            pointRecordQuery.setUserId(xsEvaluateRecord.getUserId());
        }
        pointRecordQuery.setExceptChannelIds(exceptChannelIds);
        pointRecordQuery.setStartTime(startTime);
        pointRecordQuery.setEndTime(endTime);

        List<XsPointRecord> pointList = xsPointRecordMapper.getListByCondition(pointRecordQuery);

        //设置加分 减分内容
        evaluateAskDataConvert.setObjList(pointList, plusComment, minusComment);

        //生成咒语
        return generateMagicSpell(userRoleName, studentName, term, plusComment, minusComment,
                characterTagList, termName, sectionCode);
    }

    /**
     * @param userRoleName     小学二年级的班主任
     * @param studentName      学生名字
     * @param term             本学期/最近
     * @param plusComment      加分明细列表
     * @param minusComment     减分明细列表
     * @param characterTagList 性格标签列表
     * @param termName         期末/日常
     * @param sectionCode      学段code
     * @return
     */
    @Override
    public String generateMagicSpell(String userRoleName, String studentName, String term, List<String> plusComment, List<String> minusComment,
                                     List<String> characterTagList, String termName, String sectionCode) {
        // 使用StringBuilder拼接咒语
        StringBuilder magicSpellBuilder = new StringBuilder();

        // 第一部分
        magicSpellBuilder.append("你是一个").append(userRoleName).append("，请根据你们班")
                .append(studentName).append("同学在").append(term).append("的表现，写一段").append(termName).append("评语。");

        // 加减分都不为空
        if (!CollUtil.isEmpty(plusComment) || !CollUtil.isEmpty(minusComment)) {
            magicSpellBuilder.append("\n");
            //无加分
            if (CollUtil.isEmpty(plusComment)) {
                //仅有减分
                if (!CollUtil.isEmpty(minusComment)) {

                    String minusStr = StringUtils.join(minusComment, "、");
                    magicSpellBuilder.append(studentName).append("在").append(term).append("的表现中：")
                            .append("；需要更加注意改进提升的是:").append(minusStr).append("。");
                }
            } else {
                //有加分
                String plusStr = StringUtils.join(plusComment, "、");

                magicSpellBuilder.append(studentName).append("在").append(term).append("的表现如下：")
                        .append(plusStr).append("；");

                //有减分
                if (!CollUtil.isEmpty(minusComment)) {
                    String minusStr = StringUtils.join(minusComment, "、");

                    magicSpellBuilder.append("需要更加注意改进提升的是:").append(minusStr).append("。");
                }
            }
        }

        // 性格部分
        if (!CollUtil.isEmpty(characterTagList)) {
            String characterTagStr = StringUtils.join(characterTagList, "、");
            magicSpellBuilder.append("\n该同学的特点是：").append(characterTagStr).append("。");
        }
        magicSpellBuilder.append("\n").append(termName).append("评语需满足以下5个条件：\n")
                .append("1. 评语要有温度、有文采；\n")
                .append("2. 只引用1句名人名言或者古诗词；\n")
                .append("3. 字数不要太长，保持300字以内；\n");

        //如果是小学
        if (sectionCode.equals(SaasSectionEnum.XX.getCode())) {

            String nickname = studentName;
            //昵称是姓名的后两个字
            if (studentName.length() > 1) {
                nickname = studentName.substring(studentName.length() - 2);
            }
            magicSpellBuilder.append("4. 不要直接称呼学生的姓名，叫得亲切一些，例如").append(nickname).append("、宝贝、宝、宝儿；\n")
                    .append("5. 合理使用标点符号。");

        } else {
            //非小学
            magicSpellBuilder.append("4. 合理使用标点符号。");
        }

        return magicSpellBuilder.toString();
    }

    @Override
    public String defaultEvaluate(String studentId) {

        StudentQuery studentQuery = new StudentQuery();
        studentQuery.setStudentId(studentId);
        List<XsStudent> xsStudentList = xsStudentMapper.getListByCondition(studentQuery);

        String studentName = "";

        if (!CollUtil.isEmpty(xsStudentList)) {
            studentName = xsStudentList.get(0).getStudentName();
        }

        //取系统模版数据
        EvaluateDetailQuery evaluateDetailQuery = new EvaluateDetailQuery();
        evaluateDetailQuery.setEvaluateRecordId("-1");
        evaluateDetailQuery.setSaasClassId("-1");
        evaluateDetailQuery.setIncludeEvaluateData(EnabledConst.YES);
        List<XsEvaluateDetail> studentEvaluateDetails = xsEvaluateDetailMapper.getListByCondition(evaluateDetailQuery);

        if (CollUtil.isEmpty(studentEvaluateDetails)) {
            return "";
        }

        //随机取一条
        XsEvaluateDetail evaluateDetail = studentEvaluateDetails.get(RandomUtil.randomInt(0, studentEvaluateDetails.size()));

        //模版内容替换
        return evaluateDetail.getEvaluateData().replace("$name$", studentName);

    }
}

package com.hailiang.edu.xsjlqueue.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.hailiang.edu.xsjlqueue.component.TimeComponent;
import com.hailiang.edu.xsjlqueue.consts.*;
import com.hailiang.edu.xsjlqueue.convert.*;
import com.hailiang.edu.xsjlqueue.convert.mapstruct.FamilyMemberDtoStruct;
import com.hailiang.edu.xsjlqueue.dal.biz.mongodb.AwardRecordOtherStorage;
import com.hailiang.edu.xsjlqueue.dal.biz.mongodb.SmsReportOtherStorage;
import com.hailiang.edu.xsjlqueue.dal.biz.mongodb.SmsReportPointStorage;
import com.hailiang.edu.xsjlqueue.dal.biz.mongodb.SmsReportStudentEvaluateStorage;
import com.hailiang.edu.xsjlqueue.dal.dao.*;
import com.hailiang.edu.xsjlqueue.dal.entity.*;
import com.hailiang.edu.xsjlqueue.dal.entity.mongodb.AwardRecordOther;
import com.hailiang.edu.xsjlqueue.dal.entity.mongodb.SmsReportOther;
import com.hailiang.edu.xsjlqueue.dal.entity.mongodb.SmsReportPoint;
import com.hailiang.edu.xsjlqueue.dal.entity.mongodb.SmsReportStudentEvaluate;
import com.hailiang.edu.xsjlqueue.dto.jxgy.StudentDto;
import com.hailiang.edu.xsjlqueue.dto.jxgy.req.FamilyStatusReq;
import com.hailiang.edu.xsjlqueue.dto.jxgy.resp.FamilyStatusResp;
import com.hailiang.edu.xsjlqueue.dto.saas.resp.ParentListRespDto;
import com.hailiang.edu.xsjlqueue.dto.saas.resp.TeachManageStaffTeachRespDto;
import com.hailiang.edu.xsjlqueue.dto.sms.SmsRuleTaskDto;
import com.hailiang.edu.xsjlqueue.dto.sms.SubjectDto;
import com.hailiang.edu.xsjlqueue.enums.RoleEnum;
import com.hailiang.edu.xsjlqueue.enums.SmsContentRangeEnum;
import com.hailiang.edu.xsjlqueue.enums.SmsRuleTypeEnum;
import com.hailiang.edu.xsjlqueue.query.*;
import com.hailiang.edu.xsjlqueue.query.sms.SmsRuleQuery;
import com.hailiang.edu.xsjlqueue.remote.biz.jxgy.JxgyClient;
import com.hailiang.edu.xsjlqueue.remote.biz.saas.SaasClient;
import com.hailiang.edu.xsjlqueue.service.JxgySendMsgService;
import com.hailiang.edu.xsjlqueue.service.RoleService;
import com.hailiang.edu.xsjlqueue.service.SaasService;
import com.hailiang.edu.xsjlqueue.service.SmsReportService;
import com.hailiang.edu.xsjlqueue.service.impl.transactional.SmsReportServiceTransactional;
import com.hailiang.edu.xsjlqueue.util.LogFormatUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SmsReportServiceImpl implements SmsReportService {

    @Resource
    XsSmsRuleMapper xsSmsRuleMapper;
    @Resource
    XsClassStudentMapper xsClassStudentMapper;
    @Resource
    XsPointRecordMapper xsPointRecordMapper;
    @Resource
    XsPlanTagMapper xsPlanTagMapper;
    @Resource
    TimeComponent timeComponent;
    @Resource
    SmsReportConvert smsReportConvert;
    @Resource
    SmsReportStudentConvert smsReportStudentConvert;
    @Resource
    SmsReportPointConvert smsReportPointConvert;
    @Resource
    SmsReportOtherConvert smsReportOtherConvert;
    @Resource
    SmsReportEvaluateConvert smsReportEvaluateConvert;
    @Resource
    RoleService roleService;
    @Resource
    SaasService saasService;
    @Resource
    XsTrustLoginMapper xsTrustLoginMapper;
    @Resource
    SmsReportServiceTransactional smsReportServiceTransactional;
    @Resource
    SmsReportPointStorage smsReportPointStorage;
    @Resource
    SmsReportOtherStorage smsReportOtherStorage;
    @Resource
    SmsReportStudentEvaluateStorage smsReportStudentEvaluateStorage;
    @Resource
    SaasClient saasClient;
    @Resource
    LogFormatUtil logFormatUtil;
    @Resource
    XsSmsReportStudentMapper xsSmsReportStudentMapper;
    @Resource
    XsStudentMapper xsStudentMapper;
    @Resource
    JxgyClient jxgyClient;
    @Resource
    JxgySendMsgService jxgySendMsgService;
    @Resource
    XsPlanUserMapper xsPlanUserMapper;
    @Resource
    FamilyMemberDtoStruct familyMemberDtoStruct;
    @Resource
    XsAvatarMapper xsAvatarMapper;
    @Resource
    PlanGroupDetailDtoConvert planGroupDetailDtoConvert;
    @Resource
    XsAwardRecordMapper xsAwardRecordMapper;
    @Resource
    AwardRecordOtherStorage awardRecordOtherStorage;
    @Resource
    XsEvaluateRecordMapper xsEvaluateRecordMapper;
    @Resource
    XsEvaluateDetailMapper xsEvaluateDetailMapper;


    @Override
    public void doGenSmsReport(SmsRuleTaskDto smsRuleTaskDto) {

        if (smsRuleTaskDto == null) {
            return;
        }
        SmsRuleQuery smsRuleQuery = new SmsRuleQuery();
        smsRuleQuery.setIsEnabled(EnabledConst.YES);
        smsRuleQuery.setSmsRuleId(smsRuleTaskDto.getSmsRuleId());

        XsSmsRule currentRule = xsSmsRuleMapper.getRowByCondition(smsRuleQuery);
        if (currentRule == null) {
            logFormatUtil.formatInfo("规则不存在");
            return;
        }

        //需要推送的用户
        Set<Long> userIds = new HashSet<>();
        userIds.add(currentRule.getUserId());

        //需要推送的学生
        Set<Long> studentIds = new HashSet<>();

        //获取快照明细，方案标签，班级下的学生列表数据
        List<XsClassStudent> xsClassStudentList = xsClassStudentMapper.getListByClassId(currentRule.getSaasClassId());
        if (CollectionUtils.isEmpty(xsClassStudentList)) {
            logFormatUtil.formatInfo("推送规则id:" + currentRule.getSmsRuleId() + " 班级下无学生");
            return;
        }

        //查询业务头像信息
        XsAvatarQuery xsAvatarQuery = new XsAvatarQuery();
        xsAvatarQuery.setSaasClassId(currentRule.getSaasClassId());
        xsAvatarQuery.setBusinessType(AvatarBusinessTypeConst.STUDENT);
        List<XsAvatar> studentAvatarList = xsAvatarMapper.getListByCondition(xsAvatarQuery);
        //重新填充学生头像值
        planGroupDetailDtoConvert.overwriteStudentIcon(xsClassStudentList, studentAvatarList);


        Date currentDate = DateUtil.date();

        PointRecordQuery pointRecordQuery = new PointRecordQuery();

        //积分兑换对数据去除
        List<Long> exceptChannelIds = new ArrayList<>();
        exceptChannelIds.add((long) XsPointRecord.CHANNEL_PRIZE);
        pointRecordQuery.setExceptChannelIds(exceptChannelIds);
        pointRecordQuery.setSaasClassId(currentRule.getSaasClassId());

        if (SmsRuleTypeEnum.PERIOD.getType().equals(currentRule.getRuleType())) {
            if (currentRule.getIsContainHelp().equals(ContainHelpConst.YES)) {
                pointRecordQuery.setScene(XsPointRecord.SCENE_PERSONAL);
            }
            if (currentRule.getTimeType().equals(TimeTypeConst.WEEK)) {
                timeComponent.setWeekTime(pointRecordQuery, currentDate);
            }
            if (currentRule.getTimeType().equals(TimeTypeConst.MONTH)) {
                timeComponent.setMonthTime(pointRecordQuery, currentDate);
            }
            if (currentRule.getTimeType().equals(TimeTypeConst.DEFINE)) {
                timeComponent.setDefineTime(pointRecordQuery, currentDate, currentRule.getDayNumber());
            }
        } else {
            pointRecordQuery.setStartTime(currentRule.getStartTime());
            pointRecordQuery.setEndTime(currentRule.getEndTime());
        }

        if (currentRule.getDataRange().equals(DataRangeConst.PLAN)) {
            //方案是否有效 方案下是否有用户 无用户则直接退出
            PlanUserQuery planUserQuery = new PlanUserQuery();
            planUserQuery.setPlanId(currentRule.getPlanId());
            List<XsPlanUser> planUserList = xsPlanUserMapper.getListByCondition(planUserQuery);
            if (CollUtil.isEmpty(planUserList)) {
                logFormatUtil.formatInfo("推送规则id:" + currentRule.getSmsRuleId() + "==方案id为" + currentRule.getPlanId() + "的方案下无用户");
                return;
            }

            pointRecordQuery.setPlanId(currentRule.getPlanId());
        }

        if (currentRule.getDataRange().equals(DataRangeConst.CLASS)) {
            //代表全班
            PlanUserQuery planUserQuery = new PlanUserQuery();
            planUserQuery.setSaasClassId(currentRule.getSaasClassId());
            List<XsPlanUser> xsPlanUserList = xsPlanUserMapper.getListByCondition(planUserQuery);
            if (!CollectionUtils.isEmpty(xsPlanUserList)) {
                Set<Long> planIds = xsPlanUserList.stream().map(XsPlanUser::getPlanId).collect(Collectors.toSet());
                pointRecordQuery.setPlanIds(planIds);
            } else {
                //设置一个查不到的值

                logFormatUtil.formatInfo("无有效方案参与人");
                return;
//                pointRecordQuery.setSaasClassId("-9999");
            }

        }

        logFormatUtil.formatInfo("pointRecordQuery:" + JSONObject.toJSONString(pointRecordQuery));
        //根据条件获取 点评记录数据
        List<XsPointRecord> xsPointRecordList = xsPointRecordMapper.getListByCondition(pointRecordQuery);

        //如果推送内容包含 奖状数据
        List<XsAwardRecord> awardRecordList = new ArrayList<>();
        if (currentRule.getSmsContentRange() != null && StrUtil.split(currentRule.getSmsContentRange(), ",")
                .contains(SmsContentRangeEnum.AWARD.getCode())) {

            AwardRecordQuery awardRecordQuery = getAwardRecordQuery(pointRecordQuery);
            logFormatUtil.formatInfo("awardRecordQuery:" + JSONObject.toJSONString(awardRecordQuery));
            //根据条件获取 发奖状记录数据
            awardRecordList = xsAwardRecordMapper.getListByCondition(awardRecordQuery);

            logFormatUtil.formatInfo("推送规则id:" + currentRule.getSmsRuleId() + " 包含奖状数据");

        }

        List<XsEvaluateDetail> evaluateDetails = new ArrayList<>();
        XsEvaluateRecord lastEvaluateRecord = null;
        if (currentRule.getSmsContentRange() != null && CharSequenceUtil.split(currentRule.getSmsContentRange(), ",")
                .contains(SmsContentRangeEnum.EVALUATE.getCode())) {
            EvaluateRecordQuery evaluateRecordQuery = new EvaluateRecordQuery();
            evaluateRecordQuery.setSaasClassId(currentRule.getSaasClassId());
            PlanUserQuery planUserQuery = new PlanUserQuery();
            if (currentRule.getDataRange().equals(DataRangeConst.CLASS)) {
                evaluateRecordQuery.setRoleCode(RoleEnum.HEAD_TEACHER.getCode());
            } else {
                planUserQuery.setPlanId(currentRule.getPlanId());
                List<XsPlanUser> planUserList = xsPlanUserMapper.getListByCondition(planUserQuery);
                List<Long> planUserIds = planUserList.stream().map(XsPlanUser::getUserId).collect(Collectors.toList());
                evaluateRecordQuery.setUserIds(planUserIds);
            }
            evaluateRecordQuery.setEvaluateStatus(EvaluateStatusConst.SAVE);
            evaluateRecordQuery.setStartTime(currentRule.getStartTime());
            evaluateRecordQuery.setEndTime(currentRule.getEndTime());
            List<XsEvaluateRecord> evaluateRecords = xsEvaluateRecordMapper.getListByCondition(evaluateRecordQuery);

            if (!CollectionUtils.isEmpty(evaluateRecords)) {
                ListUtil.sort(evaluateRecords, Comparator.comparing(XsEvaluateRecord::getEvaluateRecordId));
                ListUtil.reverse(evaluateRecords);
                lastEvaluateRecord = evaluateRecords.get(0);
                // 查询需要的评语
                EvaluateDetailQuery evaluateDetailQuery = new EvaluateDetailQuery();
                evaluateDetailQuery.setEvaluateRecordId(String.valueOf(lastEvaluateRecord.getEvaluateRecordId()));
                evaluateDetailQuery.setSaasClassId(currentRule.getSaasClassId());
                evaluateDetailQuery.setIncludeEvaluateData(EnabledConst.YES);
                evaluateDetails = xsEvaluateDetailMapper.getListByCondition(evaluateDetailQuery);
                evaluateDetails = evaluateDetails.stream().filter(p -> CharSequenceUtil.isNotEmpty(p.getEvaluateData())).collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isEmpty(xsPointRecordList) && CollectionUtils.isEmpty(awardRecordList)
                && CollectionUtils.isEmpty(evaluateDetails)) {
            logFormatUtil.formatInfo("推送规则id:" + currentRule.getSmsRuleId() + " 全部学生无积分明细记录、奖状记录、评语记录");
            return;
        }

        List<XsPlanTag> xsPlanTagList = new ArrayList<>();
        if (currentRule.getDataRange().equals(DataRangeConst.PLAN)) {
            PlanTagQuery planTagQuery = new PlanTagQuery();
            planTagQuery.setPlanId(currentRule.getPlanId());
            planTagQuery.setSaasClassId(currentRule.getSaasClassId());
            xsPlanTagList = xsPlanTagMapper.getListByCondition(planTagQuery);
        }

        if (!CollectionUtils.isEmpty(xsPointRecordList)) {
            Set<Long> pointUserIds = xsPointRecordList.stream().map(XsPointRecord::getUserId).collect(Collectors.toSet());
            Set<Long> pointStudentIds = xsPointRecordList.stream().map(XsPointRecord::getStudentId).collect(Collectors.toSet());

            userIds.addAll(pointUserIds);
            //塞入 有点评数据需要推送的学生
            studentIds.addAll(pointStudentIds);
        }

        if (!CollectionUtils.isEmpty(awardRecordList)) {
            Set<Long> awardUserIds = awardRecordList.stream().map(XsAwardRecord::getUserId).collect(Collectors.toSet());
            userIds.addAll(awardUserIds);

            Set<Long> awardRecordIds = awardRecordList.stream().map(XsAwardRecord::getAwardRecordId).collect(Collectors.toSet());
            //奖状发布记录详情数据
            List<AwardRecordOther> awardRecordOtherList = awardRecordOtherStorage.getListByAwardRecordIds(awardRecordIds);

            //塞入 有奖状数据需要推送的学生
            setAwardStudentIds(studentIds, awardRecordOtherList);
        }

        if (Objects.nonNull(lastEvaluateRecord) && !CollectionUtils.isEmpty(evaluateDetails)) {
            userIds.add(lastEvaluateRecord.getUserId());
            List<Long> evaluateStudentIds = evaluateDetails.stream().map(XsEvaluateDetail::getStudentId).collect(Collectors.toList());
            studentIds.addAll(evaluateStudentIds);
        }

        TrustLoginQuery trustLoginQuery = new TrustLoginQuery();
        trustLoginQuery.setPlatformCode("saas");
        trustLoginQuery.setUserIds(userIds);
        List<XsTrustLogin> xsTrustLoginList = xsTrustLoginMapper.getListByCondition(trustLoginQuery);

        //userId -> openId
        Map<Long, Long> userIdOpenIdMap = xsTrustLoginList.stream()
                .collect(Collectors.toMap(XsTrustLogin::getUserId, input -> Long.valueOf(input.getOpenId()), (p1, p2) -> p2));

        Map<Long, Long> userIdWithStaffIdMap = saasClient.getUserIdMapStaffId(userIdOpenIdMap, currentRule.getSaasTenantId());

        //获取员工在对应学校教职工的任职情况
        List<TeachManageStaffTeachRespDto> teachManageStaffTeachRespDtos = saasService.getTeachManageList(userIdWithStaffIdMap, currentRule.getSaasSchoolId());
        //获取用户id 对应角色code的map映射
        Map<Long, RoleEnum> userIdWithRoleCodeMap = roleService.getUserIdWithRoleCodeMap(userIdWithStaffIdMap, currentRule.getSaasClassId(), teachManageStaffTeachRespDtos);

        // 获取用户id 对应教授学科的map映射
        Map<Long, List<SubjectDto>> userIdWithSubjectListMap = roleService.getUserIdWithSubjectListMap(userIdWithStaffIdMap, currentRule.getSaasClassId(), teachManageStaffTeachRespDtos);


        //生成报告对象
        XsSmsReport xsSmsReport = smsReportConvert.toObj(pointRecordQuery, currentRule);
        //生成报告学生关系对象列表
        List<XsSmsReportStudent> xsSmsReportStudentList = smsReportStudentConvert.toObjList(xsSmsReport, xsClassStudentList);

        //只推送当前在读、且本期有数据的学生报告 或本期有奖状记录数据的学生；全部学生无记录时，不需要推送；
        xsSmsReportStudentList = xsSmsReportStudentList.stream().filter(t -> studentIds.contains(t.getStudentId())).collect(Collectors.toList());

        logFormatUtil.formatInfo("推送规则id:" + currentRule.getSmsRuleId() + " 此次报告关联学生:" + JSONObject.toJSONString(xsSmsReportStudentList));

        //获取报告明细列表
        List<SmsReportPoint> smsReportPointList = smsReportPointConvert.toObjList(xsSmsReport, xsPointRecordList, userIdWithRoleCodeMap, userIdWithSubjectListMap);
        //生成报告属性快照数据
        SmsReportOther smsReportOther = smsReportOtherConvert.toObj(xsSmsReport, xsPlanTagList, xsClassStudentList, userIdWithRoleCodeMap, userIdWithSubjectListMap, awardRecordList);

        //进行数据存储
        smsReportServiceTransactional.add(xsSmsReport, xsSmsReportStudentList);
        smsReportPointStorage.batchInsert(smsReportPointList);

        List<SmsReportOther> smsReportOtherList = new ArrayList<>();
        smsReportOtherList.add(smsReportOther);
        smsReportOtherStorage.batchInsert(smsReportOtherList);

        if (!CollectionUtils.isEmpty(evaluateDetails)) {
            SmsReportStudentEvaluate reportStudentEvaluate = smsReportEvaluateConvert.toReportEvaluate(xsSmsReport, lastEvaluateRecord, evaluateDetails);
            smsReportStudentEvaluateStorage.batchInsert(Collections.singletonList(reportStudentEvaluate));
        }

        //发送短信公众号等，调用家校相关接口
        sendStudentReport(xsSmsReport.getSmsReportId(), xsSmsReportStudentList, xsSmsReport, smsReportPointList, awardRecordList, evaluateDetails, userIds);

    }

    private void setAwardStudentIds(Set<Long> studentIds, List<AwardRecordOther> awardRecordOtherList) {

        if (CollUtil.isEmpty(awardRecordOtherList)) {
            return;
        }

        for (AwardRecordOther awardRecordOther : awardRecordOtherList) {
            if (!CollUtil.isEmpty(awardRecordOther.getGroupList())) {
                for (AwardRecordOther.Group group : awardRecordOther.getGroupList()) {
                    Set<Long> awardGroupStudentIds = group.getStudentList().stream().
                            map(AwardRecordOther.Student::getStudentId).collect(Collectors.toSet());
                    studentIds.addAll(awardGroupStudentIds);
                }
            }

            if (!CollUtil.isEmpty(awardRecordOther.getDetails())) {

                List<AwardRecordOther.AwardRecordDetail> filterAwardList = awardRecordOther.getDetails().stream().
                        filter(t -> !t.getRankCode().equals(AwardCodeConst.RANK_GROUP)).collect(Collectors.toList());
                if (!CollUtil.isEmpty(filterAwardList)) {
                    Set<Long> awardStudentIds = filterAwardList.stream().map(AwardRecordOther.AwardRecordDetail::getBusinessId).collect(Collectors.toSet());
                    studentIds.addAll(awardStudentIds);
                }
            }
        }
    }

    private AwardRecordQuery getAwardRecordQuery(PointRecordQuery pointRecordQuery) {

        AwardRecordQuery awardRecordQuery = new AwardRecordQuery();

        if (pointRecordQuery.getPlanId() != null) {
            awardRecordQuery.setPlanId(pointRecordQuery.getPlanId());
        }

        if (pointRecordQuery.getPlanIds() != null) {
            awardRecordQuery.setPlanIds(pointRecordQuery.getPlanIds());
        }

        awardRecordQuery.setStartTime(pointRecordQuery.getStartTime());
        awardRecordQuery.setEndTime(pointRecordQuery.getEndTime());
        awardRecordQuery.setSaasClassId(pointRecordQuery.getSaasClassId());
        awardRecordQuery.setIsDuplicateTime(EnabledConst.YES);


        return awardRecordQuery;
    }


    private void sendStudentReport(Long smsReportId, List<XsSmsReportStudent> xsSmsReportStudentList, XsSmsReport xsSmsReport,
                                   List<SmsReportPoint> smsReportPointList, List<XsAwardRecord> awardRecordList,
                                   List<XsEvaluateDetail> evaluateDetails, Set<Long> userIds) {

        if (CollectionUtils.isEmpty(xsSmsReportStudentList)
                || (CollectionUtils.isEmpty(smsReportPointList) && CollectionUtils.isEmpty(awardRecordList) && CollectionUtils.isEmpty(evaluateDetails))) {
            return;
        }

        //判断这批学生是否有其他报告，有代表非首次，没有代表首次
        Set<Long> studentIds = xsSmsReportStudentList.stream().map(XsSmsReportStudent::getStudentId).collect(Collectors.toSet());
        StudentQuery studentQuery = new StudentQuery();
        studentQuery.setStudentIds(studentIds);
        List<XsStudent> xsStudentList = xsStudentMapper.getListByCondition(studentQuery);


        Map<Long, String> studentIdToNo = xsStudentList.stream().collect(Collectors.toMap(XsStudent::getStudentId, XsStudent::getStudentNo, (p1, p2) -> p2));


        //有推送记录学生（代表非首次推送报告） 的学生id (推微信)
        List<Long> notFirstSendStudentIds = xsSmsReportStudentMapper.getOtherReportStudent(studentIds, smsReportId);

        Set<Long> saasStudentIds = new HashSet<>();

        for (XsStudent xsStudent : xsStudentList) {

            saasStudentIds.add(xsStudent.getStudentId());
        }

        //需要推送微信的家长
        FamilyStatusReq allFamilyStatusReq = new FamilyStatusReq();

        List<FamilyStatusResp> haiAllFamilyStatusRespList = new ArrayList<>();
        List<FamilyStatusResp> saasFamilyStatusResps = new ArrayList<>();

        if (!CollectionUtils.isEmpty(saasStudentIds)) {
            allFamilyStatusReq.setStudentIds(saasStudentIds);
            //获取所有家长信息 （家校系统）
            logFormatUtil.formatInfo("报告id:" + smsReportId + "hai家校AllFamilyStatusReq:" + JSONObject.toJSONString(allFamilyStatusReq));
            haiAllFamilyStatusRespList = jxgyClient.getFamilyStatusListByStudentIds(allFamilyStatusReq);
            logFormatUtil.formatInfo("报告id:" + smsReportId + "hai家校AllFamilyStatusRespList:" + JSONObject.toJSONString(haiAllFamilyStatusRespList));

            //获取所有家长信息saas
            List<ParentListRespDto> saasParentList = saasClient.getParentListByCondition(studentIds);
            logFormatUtil.formatInfo("报告id:" + smsReportId + "saas学生家长原始数据saasParentList:" + JSONObject.toJSONString(saasParentList));

            Map<Long, XsStudent> studentIdToMap = xsStudentList.stream().collect(Collectors.toMap(XsStudent::getStudentId, input -> input, (p1, p2) -> p2));

            //转换saas数据
            saasFamilyStatusResps = smsReportStudentConvert.toFamilyStatusResp(saasParentList, studentIdToMap);
            logFormatUtil.formatInfo("报告id:" + smsReportId + "saas数据转换familyStatusResps:" + JSONObject.toJSONString(saasFamilyStatusResps));

        }

        if (CollectionUtils.isEmpty(haiAllFamilyStatusRespList) && CollectionUtils.isEmpty(saasFamilyStatusResps)) {
            logFormatUtil.formatInfo("报告id:" + smsReportId + "所有学生无家长数据 直接退出:");
            return;
        }

        //数据合并
        List<FamilyStatusResp> allfamilyStatusRespList = smsReportStudentConvert.mergeData(haiAllFamilyStatusRespList, saasFamilyStatusResps);
        logFormatUtil.formatInfo("报告id:" + smsReportId + "合并后家长数据:" + JSONObject.toJSONString(allfamilyStatusRespList));

        //待发短信模版
        List<StudentDto> waitMsgStudentList = new ArrayList<>();
        //待发公众号信息
        List<StudentDto> waitWxStudentList = new ArrayList<>();

        for (FamilyStatusResp familyStatusResp : allfamilyStatusRespList) {

            //首次推送 发短信
            if (CollectionUtils.isEmpty(notFirstSendStudentIds) || !notFirstSendStudentIds.contains(Long.valueOf(familyStatusResp.getStudentId()))) {
                logFormatUtil.formatInfo("报告id:" + smsReportId + familyStatusResp.getStudentName() + "首次推送 (短信):" + JSONObject.toJSONString(familyStatusResp));

                StudentDto studentDto = new StudentDto();
                studentDto.setStudentId(familyStatusResp.getStudentId());
                studentDto.setStudentCode(studentIdToNo.getOrDefault(Long.valueOf(familyStatusResp.getStudentId()), "111"));
                studentDto.setStudentName(familyStatusResp.getStudentName());
                studentDto.setFamilyStatus(familyStatusResp.getFamilyStatus());

                List<String> familyMobiles = new ArrayList<>();
                for (FamilyStatusResp.FamilyMemberDto familyMemberDto : familyStatusResp.getFamilyMember()) {
                    familyMobiles.add(familyMemberDto.getMobile());
                }
                studentDto.setFamilyMobiles(familyMobiles);
                studentDto.setFamilyMember(familyMemberDtoStruct.toObjList(familyStatusResp.getFamilyMember()));

                waitMsgStudentList.add(studentDto);

            } else {
                logFormatUtil.formatInfo("报告id:" + smsReportId + familyStatusResp.getStudentName() + "非首次推送(微信):" + JSONObject.toJSONString(familyStatusResp));

                if (familyStatusResp.getFamilyStatus().equals(FamilyStatusResp.FAMILY_STATUS_YES)) {
                    //发todo 公众号信息
                    StudentDto dto = new StudentDto();
                    dto.setStudentId(familyStatusResp.getStudentId());
                    dto.setStudentCode(studentIdToNo.getOrDefault(Long.valueOf(familyStatusResp.getStudentId()), "111"));
                    dto.setStudentName(familyStatusResp.getStudentName());
                    dto.setFamilyStatus(familyStatusResp.getFamilyStatus());

                    dto.setFamilyMember(familyMemberDtoStruct.toObjList(familyStatusResp.getFamilyMember()));


                    waitWxStudentList.add(dto);

                } else {
                    logFormatUtil.formatInfo("报告id:" + smsReportId + familyStatusResp.getStudentName() + "非首次推送 家长无激活数据(短信):" + JSONObject.toJSONString(familyStatusResp));

                    //发todo 短信
                    StudentDto studentDto = new StudentDto();
                    studentDto.setStudentId(familyStatusResp.getStudentId());
                    studentDto.setStudentCode(studentIdToNo.getOrDefault(Long.valueOf(familyStatusResp.getStudentId()), "111"));
                    studentDto.setStudentName(familyStatusResp.getStudentName());
                    studentDto.setFamilyStatus(familyStatusResp.getFamilyStatus());
                    studentDto.setFamilyMember(familyMemberDtoStruct.toObjList(familyStatusResp.getFamilyMember()));

                    List<String> familyMobiles = new ArrayList<>();
                    for (FamilyStatusResp.FamilyMemberDto familyMemberDto : familyStatusResp.getFamilyMember()) {
                        familyMobiles.add(familyMemberDto.getMobile());
                    }
                    studentDto.setFamilyMobiles(familyMobiles);


                    waitMsgStudentList.add(studentDto);
                }
            }
        }

        logFormatUtil.formatInfo("报告id:" + smsReportId + "最终推送短信的学生数据:waitMsgStudentList:" + JSONObject.toJSONString(waitMsgStudentList));
        logFormatUtil.formatInfo("报告id:" + smsReportId + "最终推送微信的学生数据:waitWxStudentList:" + JSONObject.toJSONString(waitWxStudentList));

        jxgySendMsgService.sendWxMsg(waitMsgStudentList, waitWxStudentList, xsSmsReport, userIds);

    }

}

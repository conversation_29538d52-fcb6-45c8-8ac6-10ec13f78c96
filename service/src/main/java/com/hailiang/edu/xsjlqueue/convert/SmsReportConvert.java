package com.hailiang.edu.xsjlqueue.convert;


import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.hailiang.edu.xsjlqueue.component.manager.IdManageComponent;
import com.hailiang.edu.xsjlqueue.consts.DeletedConst;
import com.hailiang.edu.xsjlqueue.dal.entity.XsSmsReport;
import com.hailiang.edu.xsjlqueue.dal.entity.XsSmsRule;
import com.hailiang.edu.xsjlqueue.query.PointRecordQuery;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Component
public class SmsReportConvert {

    @Resource
    IdManageComponent idManageComponent;

    public XsSmsReport toObj(PointRecordQuery pointRecordQuery, XsSmsRule xsSmsRule) {

        XsSmsReport xsSmsReport = new XsSmsReport();
        xsSmsReport.setSmsReportId(idManageComponent.nextId());
        xsSmsReport.setUserId(xsSmsRule.getEditUserId());
        xsSmsReport.setSendHour(xsSmsRule.getSendHour());
        xsSmsReport.setSendMinute(xsSmsRule.getSendMinute());
        xsSmsReport.setSmsRuleId(xsSmsRule.getSmsRuleId());
        xsSmsReport.setSmsContentRange(xsSmsRule.getSmsContentRange());
        xsSmsReport.setStartTime(pointRecordQuery.getStartTime());
        xsSmsReport.setEndTime(pointRecordQuery.getEndTime());
        xsSmsReport.setIsDeleted(DeletedConst.NO);
        xsSmsReport.setSaasClassId(xsSmsRule.getSaasClassId());
        xsSmsReport.setSaasSchoolId(xsSmsRule.getSaasSchoolId());
        xsSmsReport.setSaasTenantId(xsSmsRule.getSaasTenantId());
        xsSmsReport.setCreateTime(DateUtil.now());
        xsSmsReport.setUpdateTime(DateUtil.now());
        xsSmsReport.setSmsRuleMetaData(JSONObject.toJSONString(xsSmsRule));
        return xsSmsReport;
    }
}

/**
 * Hailiang.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.hailiang.edu.xsjlqueue.mq;

import java.util.UUID;

import javax.annotation.Resource;

import com.hailiang.edu.xsjlqueue.dal.biz.StudentSyncStorage;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.hailiang.edu.xsjlqueue.consts.CommonConst;
import com.hailiang.edu.xsjlqueue.dto.stu.StudentDataSyncTaskDto;
import com.hailiang.edu.xsjlqueue.service.StudentDataSyncService;
import com.hailiang.edu.xsjlqueue.util.LogFormatUtil;
import com.hailiang.edu.xsjlqueue.util.Md5Util;
import com.hailiang.edu.xsjlqueue.util.RedisKeyUtil;
import com.hailiang.edu.xsjlqueue.util.RedisUtil;
import com.hailiang.hr.common.hr.threadCache.ReqThreadCache;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @version v0.1: StudentDataSyncTaskListener.java, v 0.1 2023年09月19日 18:00  zhousx Exp $
 */
@Component
@RocketMQMessageListener(consumerGroup = "${rocketmq.consumer.sync.group}", topic = "${rocketmq.sync.topic}", consumeThreadMax = 1)
public class StudentDataSyncTaskListener implements RocketMQListener<String> {
    @Resource
    private LogFormatUtil          logFormatUtil;

    @Resource
    private RedisUtil              redisUtil;

    @Resource
    private StudentDataSyncService studentDataSyncService;

    /**
     * 用来存放taskId，防重复消费时间限制（单位s）
     */
    private static long            TASKID_EXPIRATION = 3 * 24 * 60 * 60;

    @Resource
    StudentSyncStorage studentSyncStorage;

    @Override
    public void onMessage(String message) {
        // 1.设置日志唯一id
        ReqThreadCache.setReqId(Md5Util.stringToMD5(UUID.randomUUID().toString()));
        logFormatUtil.formatInfo("StudentDataSyncTaskListener入参------:" + message);

        if (StrUtil.isEmpty(message)) {
            logFormatUtil.formatInfo("StudentDataSyncTaskListener---failed消息参数校验失败------:参数为空");
            return;
        }
        try {
            StudentDataSyncTaskDto taskDto = JSONObject.parseObject(message, StudentDataSyncTaskDto.class);
            if (repeatConsume(taskDto.getStuSyncTaskId())) {
                return;
            }

            studentSyncStorage.setStuSyncCount(taskDto);

            logFormatUtil.formatInfo("开始处理:" + message);

            studentDataSyncService.doStuSync(taskDto);

        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
        }
    }

    /**
     * 判断是否重复消费
     *
     * @param taskId 任务id
     * @return boolean
     */
    private boolean repeatConsume(Long taskId) {
        String key = RedisKeyUtil.getRedisKey(CommonConst.SEND_STU_SYNC_MSG_KEY, taskId);
        if (!redisUtil.setIfAbsent(key, "true", TASKID_EXPIRATION)) {
            //增加消费记录
            return true;
        }
        return false;
    }
}
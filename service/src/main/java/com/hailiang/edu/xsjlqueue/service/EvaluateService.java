package com.hailiang.edu.xsjlqueue.service;

import com.hailiang.edu.xsjlqueue.dto.evaluate.EvaluateTaskDto;
import com.hailiang.edu.xsjlqueue.exception.BusinessException;

import java.util.List;

public interface EvaluateService {

    void doGenEvaluate(EvaluateTaskDto evaluateTaskDto) throws BusinessException;

    String generateMagicSpell(String userRoleName, String studentName, String term, List<String> plusComment, List<String> minusComment,
                              List<String> characterTagList, String termName, String phaseCode);

    /**
     * 体验班学生 取系统默认评语
     * @param studentId
     * @return
     */
    String defaultEvaluate(String studentId);
}

package com.hailiang.edu.xsjlqueue.validate;



import com.hailiang.edu.xsjlqueue.dto.ResultJson;
import com.hailiang.edu.xsjlqueue.enums.ApiCodeEnum;
import net.sf.oval.ConstraintViolation;
import net.sf.oval.Validator;

import java.util.List;

/**
 * 基础验证器
 *
 * @param <T>
 * <AUTHOR>
 */
public class BaseValidate<T> {

    public ResultJson execute(T ob, String profile) {
        Validator validator = new Validator();
        List<ConstraintViolation> message = validator.validate(ob, profile);

        if (!message.isEmpty()) {
            String msg = message.get(0).getMessage();
            return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(), msg);
        }

        return new ResultJson(ApiCodeEnum.SUCCESS.getCode(), ApiCodeEnum.SUCCESS.getMsg());
    }
}






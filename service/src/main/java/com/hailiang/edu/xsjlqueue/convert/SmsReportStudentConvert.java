package com.hailiang.edu.xsjlqueue.convert;

import cn.hutool.core.date.DateUtil;
import com.hailiang.edu.xsjlqueue.consts.DeletedConst;
import com.hailiang.edu.xsjlqueue.dal.entity.XsClassStudent;
import com.hailiang.edu.xsjlqueue.dal.entity.XsSmsReport;
import com.hailiang.edu.xsjlqueue.dal.entity.XsSmsReportStudent;
import com.hailiang.edu.xsjlqueue.dal.entity.XsStudent;
import com.hailiang.edu.xsjlqueue.dto.jxgy.resp.FamilyStatusResp;
import com.hailiang.edu.xsjlqueue.dto.saas.resp.ParentListRespDto;
import com.hailiang.edu.xsjlqueue.util.SetUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class SmsReportStudentConvert {

    @Resource
    SetUtil<String> stringSetUtil;


    public List<XsSmsReportStudent> toObjList(XsSmsReport xsSmsReport, List<XsClassStudent> xsClassStudentList) {

        if (CollectionUtils.isEmpty(xsClassStudentList)) {
            return new ArrayList<>();
        }

        List<XsSmsReportStudent> xsSmsReportStudentList = new ArrayList<>();
        for (XsClassStudent xsClassStudent : xsClassStudentList) {
            XsSmsReportStudent xsSmsReportStudent = new XsSmsReportStudent();
            xsSmsReportStudent.setSmsReportId(xsSmsReport.getSmsReportId());
            xsSmsReportStudent.setStudentId(xsClassStudent.getStudentId());
            xsSmsReportStudent.setIsDeleted(DeletedConst.NO);
            xsSmsReportStudent.setSaasClassId(xsSmsReport.getSaasClassId());
            xsSmsReportStudent.setSaasSchoolId(xsSmsReport.getSaasSchoolId());
            xsSmsReportStudent.setSaasTenantId(xsSmsReport.getSaasTenantId());
            xsSmsReportStudent.setCreateTime(DateUtil.now());
            xsSmsReportStudent.setUpdateTime(DateUtil.now());
            xsSmsReportStudentList.add(xsSmsReportStudent);
        }

        return xsSmsReportStudentList;

    }

    public List<FamilyStatusResp> toFamilyStatusResp(List<ParentListRespDto> saasParentList, Map<Long, XsStudent> studentIdToMap) {

        if (CollectionUtils.isEmpty(saasParentList) || CollectionUtils.isEmpty(studentIdToMap)) {
            return new ArrayList<>();
        }

        Map<Long, ParentListRespDto> studentIdMapToList = saasParentList.stream().collect(Collectors.toMap(ParentListRespDto::getStudentId, input -> input, (p1, p2) -> p2));


        List<FamilyStatusResp> familyStatusResps = new ArrayList<>();

        for (Map.Entry<Long, XsStudent> entry : studentIdToMap.entrySet()) {

            FamilyStatusResp familyStatusResp = new FamilyStatusResp();

            if (studentIdMapToList.containsKey(entry.getKey()) && studentIdMapToList.get(entry.getKey()).getParentInfos() != null) {

                List<ParentListRespDto.ParentInfos> parentInfos = studentIdMapToList.get(entry.getKey()).getParentInfos();

                List<FamilyStatusResp.FamilyMemberDto> familyMemberDtos = new ArrayList<>();
                for (ParentListRespDto.ParentInfos parentInfo : parentInfos) {
                    FamilyStatusResp.FamilyMemberDto familyMemberDto = new FamilyStatusResp.FamilyMemberDto();

                    familyMemberDto.setMobile(parentInfo.getMobile());
                    familyMemberDto.setName(parentInfo.getName());
                    familyMemberDtos.add(familyMemberDto);
                }


                familyStatusResp.setFamilyStatus(FamilyStatusResp.FAMILY_STATUS_NO);
                familyStatusResp.setFamilyMember(familyMemberDtos);
                familyStatusResp.setStudentName(entry.getValue().getStudentName());
                familyStatusResp.setStudentId(String.valueOf(entry.getValue().getStudentId()));

                familyStatusResps.add(familyStatusResp);
            }
        }

        return familyStatusResps;

    }


    public List<FamilyStatusResp> mergeData(List<FamilyStatusResp> haiAllFamilyStatusRespList, List<FamilyStatusResp> saasParentList) {


        List<FamilyStatusResp> initList = new ArrayList<>(haiAllFamilyStatusRespList);

        if (CollectionUtils.isEmpty(initList)) {
            return saasParentList;
        }

        //todo list 合并
        Set<String> initStudentIds = initList.stream().map(FamilyStatusResp::getStudentId).collect(Collectors.toSet());

        Map<String, FamilyStatusResp> stringFamilyStatusRespMap = saasParentList.stream().collect(Collectors.toMap(FamilyStatusResp::getStudentId, input -> input, (p1, p2) -> p2));
        Set<String> saasStudentIds = saasParentList.stream().map(FamilyStatusResp::getStudentId).collect(Collectors.toSet());

        for (FamilyStatusResp familyStatusResp : initList) {
            if (!CollectionUtils.isEmpty(stringFamilyStatusRespMap) && stringFamilyStatusRespMap.containsKey(familyStatusResp.getStudentId())) {
                FamilyStatusResp saasDto = stringFamilyStatusRespMap.get(familyStatusResp.getStudentId());
                if (familyStatusResp.getStudentName().equals(saasDto.getStudentName())) {
                    //具体合并
                    familyStatusResp.getFamilyMember().addAll(saasDto.getFamilyMember());
                }
            }
        }
        Set<String> extraStudentCode = stringSetUtil.difference(saasStudentIds, initStudentIds);
        List<FamilyStatusResp> extra = new ArrayList<>();
        for (FamilyStatusResp familyStatusResp : saasParentList) {
            if (!CollectionUtils.isEmpty(extraStudentCode) && extraStudentCode.contains(familyStatusResp.getStudentId())) {
                extra.add(familyStatusResp);
            }
        }
        initList.addAll(extra);
        //todo 手机号去重
        for (FamilyStatusResp familyStatusResp : initList) {

            //去重后更新字段
            ArrayList<FamilyStatusResp.FamilyMemberDto> distinctMobile = familyStatusResp.getFamilyMember().stream()
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                            new TreeSet<>(Comparator.comparing(FamilyStatusResp.FamilyMemberDto::getMobile))), ArrayList::new));

            familyStatusResp.setFamilyMember(distinctMobile);
        }
        return initList;

    }

}

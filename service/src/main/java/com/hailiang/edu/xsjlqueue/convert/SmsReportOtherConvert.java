package com.hailiang.edu.xsjlqueue.convert;

import cn.hutool.core.collection.CollUtil;
import com.hailiang.edu.xsjlqueue.component.manager.IdManageComponent;
import com.hailiang.edu.xsjlqueue.consts.DeletedConst;
import com.hailiang.edu.xsjlqueue.convert.mapstruct.SmsReportOtherStruct;
import com.hailiang.edu.xsjlqueue.dal.entity.*;
import com.hailiang.edu.xsjlqueue.dal.entity.mongodb.SmsReportOther;
import com.hailiang.edu.xsjlqueue.dto.sms.RoleDto;
import com.hailiang.edu.xsjlqueue.dto.sms.SubjectDto;
import com.hailiang.edu.xsjlqueue.enums.RoleEnum;
import com.hailiang.edu.xsjlqueue.util.DateUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class SmsReportOtherConvert {

    @Resource
    IdManageComponent idManageComponent;
    @Resource
    SmsReportOtherStruct smsReportOtherStruct;

    public SmsReportOther toObj(XsSmsReport xsSmsReport, List<XsPlanTag> xsPlanTagList
            , List<XsClassStudent> xsClassStudentList, Map<Long, RoleEnum> userIdWithRoleCodeMap, Map<Long, List<SubjectDto>> userIdWithSubjectListMap,
                                List<XsAwardRecord> awardRecordList) {

        SmsReportOther smsReportOther = new SmsReportOther();
        smsReportOther.setSmsReportOtherId(idManageComponent.nextId());
        smsReportOther.setSmsReportId(xsSmsReport.getSmsReportId());
        smsReportOther.setUserId(xsSmsReport.getUserId());
        smsReportOther.setPlanTagList(toPlanTagObjList(xsPlanTagList));
        smsReportOther.setStudentList(toStudentObjList(xsClassStudentList));

        smsReportOther.setRoleList(new ArrayList<>());
        if (!CollectionUtils.isEmpty(userIdWithRoleCodeMap) && userIdWithRoleCodeMap.containsKey(xsSmsReport.getUserId())) {
            RoleEnum roleEnum = userIdWithRoleCodeMap.get(xsSmsReport.getUserId());
            List<RoleDto> roleDtoList = new ArrayList<>();
            RoleDto roleDto = new RoleDto();
            roleDto.setRoleCode(roleEnum.getCode());
            roleDto.setRoleName(roleEnum.getName());
            roleDtoList.add(roleDto);
            smsReportOther.setRoleList(roleDtoList);
        }

        smsReportOther.setSubjectList(new ArrayList<>());
        if (!CollectionUtils.isEmpty(userIdWithSubjectListMap) && userIdWithSubjectListMap.containsKey(xsSmsReport.getUserId())) {
            smsReportOther.setSubjectList(userIdWithSubjectListMap.get(xsSmsReport.getUserId()));

        }

        smsReportOther.setSaasClassId(xsSmsReport.getSaasClassId());
        smsReportOther.setSaasSchoolId(xsSmsReport.getSaasSchoolId());
        smsReportOther.setSaasTenantId(xsSmsReport.getSaasTenantId());
        smsReportOther.setGmtCreated(DateUtil.getDateTime());
        smsReportOther.setGmtCreatedTimeStamp(DateUtil.getCurrentTimeStamp() / 1000);
        smsReportOther.setGmtModified(DateUtil.getDateTime());
        smsReportOther.setGmtModifiedTimeStamp(DateUtil.getCurrentTimeStamp() / 1000);
        smsReportOther.setIsDeleted(DeletedConst.NO);
        smsReportOther.setAwardRecordIds(new HashSet<>());
        if (!CollUtil.isEmpty(awardRecordList)) {
            smsReportOther.setAwardRecordIds(awardRecordList.stream().map(XsAwardRecord::getAwardRecordId).collect(Collectors.toSet()));

        }

        return smsReportOther;
    }


    private List<SmsReportOther.StudentDto> toStudentObjList(List<XsClassStudent> xsClassStudentList) {
        if (CollectionUtils.isEmpty(xsClassStudentList)) {
            return new ArrayList<>();
        }

        List<SmsReportOther.StudentDto> studentDtoList = new ArrayList<>();

        for (XsClassStudent xsClassStudent : xsClassStudentList) {
            XsStudent xsStudent = xsClassStudent.getXsStudent();
            SmsReportOther.StudentDto studentDto = new SmsReportOther.StudentDto();
            studentDto.setStudentId(xsClassStudent.getStudentId());
            studentDto.setStudentNo(xsStudent.getStudentNo());
            studentDto.setStudentName(xsStudent.getStudentName());
            studentDto.setUrl(xsClassStudent.getUrl());
            studentDto.setIsLeader(xsStudent.getIsLeader());

            studentDtoList.add(studentDto);
        }

        return studentDtoList;
    }


    private List<SmsReportOther.PlanTagDto> toPlanTagObjList(List<XsPlanTag> xsPlanTagList) {

        if (CollectionUtils.isEmpty(xsPlanTagList)) {
            return new ArrayList<>();
        }

        List<SmsReportOther.PlanTagDto> planTagDtos = new ArrayList<>();
        for (XsPlanTag xsPlanTag : xsPlanTagList) {
            planTagDtos.add(smsReportOtherStruct.toObj(xsPlanTag));
        }

        return planTagDtos;
    }

}

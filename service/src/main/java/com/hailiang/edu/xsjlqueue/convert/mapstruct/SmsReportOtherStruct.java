package com.hailiang.edu.xsjlqueue.convert.mapstruct;

import com.hailiang.edu.xsjlqueue.convert.mapstruct.strategy.Int2BooleanStrategy;
import com.hailiang.edu.xsjlqueue.convert.mapstruct.strategy.String2LongStrategy;
import com.hailiang.edu.xsjlqueue.dal.entity.XsPlanTag;
import com.hailiang.edu.xsjlqueue.dal.entity.mongodb.SmsReportOther;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", uses = {Int2BooleanStrategy.class, String2LongStrategy.class})
public interface SmsReportOtherStruct {

    SmsReportOther.PlanTagDto toObj(XsPlanTag xsPlanTag);
}

package com.hailiang.edu.xsjlqueue.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.hailiang.edu.xsjlqueue.consts.PointSceneTypeConst;
import com.hailiang.edu.xsjlqueue.dal.entity.XsPointRecord;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * wz
 */
@Component
public class EvaluateAskDataConvert {

    /**
     * @param pointList
     * @param plusComment
     * @param minusComment
     * @return
     */
    public void setObjList(List<XsPointRecord> pointList, List<String> plusComment, List<String> minusComment) {

        if (CollUtil.isEmpty(pointList)) {
            return;
        }

        Map<String, Double> plusScoreMap = new HashMap<>();
        Map<String, Double> minusScoreMap = new HashMap<>();

        //帮扶正分点评数据
        List<XsPointRecord> helpPlusPoint = pointList.stream().filter(t -> t.getScene().equals(PointSceneTypeConst.HELP)
                && NumberUtil.compare(t.getScore(), 0) > 0).collect(Collectors.toList());

        if (!CollUtil.isEmpty(helpPlusPoint)) {
            double sumOfScores = helpPlusPoint.stream()
                    .mapToDouble(XsPointRecord::getScore)
                    .sum();
            plusScoreMap.put("当小师父", sumOfScores);
        }

        //积分点评数据
        List<XsPointRecord> personalPoint = pointList.stream().filter(t -> !t.getScene().equals(PointSceneTypeConst.HELP)).collect(Collectors.toList());

        if (!CollUtil.isEmpty(personalPoint)) {

            //点评正分数据
            List<XsPointRecord> plusPoint = personalPoint.stream().filter(t -> NumberUtil.compare(t.getScore(), 0) > 0).collect(Collectors.toList());

            //点评负分数据
            List<XsPointRecord> minusPoint = personalPoint.stream().filter(t -> NumberUtil.compare(t.getScore(), 0) < 0).collect(Collectors.toList());

            if (!CollUtil.isEmpty(plusPoint)) {
                //按照内容分组求和
                Map<String, Double> contentSumMap = plusPoint.stream()
                        .collect(Collectors.groupingBy(XsPointRecord::getContent,
                                Collectors.summingDouble(XsPointRecord::getScore)));

                plusScoreMap.putAll(contentSumMap);
            }

            if (!CollUtil.isEmpty(minusPoint)) {
                //按照内容分组求和
                Map<String, Double> minContentSumMap = minusPoint.stream()
                        .collect(Collectors.groupingBy(XsPointRecord::getContent,
                                Collectors.summingDouble(XsPointRecord::getScore)));
                minusScoreMap.putAll(minContentSumMap);
            }
        }

        if (!CollUtil.isEmpty(plusScoreMap)) {
            // 按值倒序排序并取前三个
            Map<String, Double> topThreeMap = plusScoreMap.entrySet().stream()
                    .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                    .limit(3)
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (e1, e2) -> e1,
                            HashMap::new
                    ));

            for (Map.Entry<String, Double> stringDoubleEntry : topThreeMap.entrySet()) {
                plusComment.add(stringDoubleEntry.getKey());
            }
        }

        if (!CollUtil.isEmpty(minusScoreMap)) {

            Map<String, Double> topOneMap = minusScoreMap.entrySet().stream()
                    .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                    .limit(1)
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (e1, e2) -> e1,
                            HashMap::new
                    ));
            for (Map.Entry<String, Double> stringDoubleEntry : topOneMap.entrySet()) {
                minusComment.add(stringDoubleEntry.getKey());
            }
        }
    }
}

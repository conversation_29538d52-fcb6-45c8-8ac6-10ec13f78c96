package com.hailiang.edu.xsjlqueue.service.impl;

import com.hailiang.edu.xsjlqueue.dto.saas.req.SaasTeachManageStaffTeachReqDto;
import com.hailiang.edu.xsjlqueue.dto.saas.resp.TeachManageStaffTeachRespDto;
import com.hailiang.edu.xsjlqueue.dto.sms.SubjectDto;
import com.hailiang.edu.xsjlqueue.enums.RoleEnum;
import com.hailiang.edu.xsjlqueue.remote.biz.saas.SaasClient;
import com.hailiang.edu.xsjlqueue.service.RoleService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class RoleServiceImpl implements RoleService {


    @Override
    public Map<Long, List<SubjectDto>> getUserIdWithSubjectListMap(Map<Long, Long> userIdWithStaffIdMap, String saasClassId, List<TeachManageStaffTeachRespDto> teachManageStaffTeachRespDtos) {

        if (CollectionUtils.isEmpty(userIdWithStaffIdMap) || CollectionUtils.isEmpty(teachManageStaffTeachRespDtos)) {
            return new HashMap<>();
        }

        Map<Long, List<SubjectDto>> map = new HashMap<>();
        for (Map.Entry<Long, Long> longLongEntry : userIdWithStaffIdMap.entrySet()) {
            List<SubjectDto> subjectDtoList = getSubjectList(longLongEntry.getValue(), Long.valueOf(saasClassId), teachManageStaffTeachRespDtos);
            map.put(longLongEntry.getKey(), subjectDtoList);
        }

        return map;
    }

    private List<SubjectDto> getSubjectList(Long staffId, Long saasClassId, List<TeachManageStaffTeachRespDto> teachManageStaffTeachRespDtos) {

        List<SubjectDto> subjectDtoList = new ArrayList<>();
        for (TeachManageStaffTeachRespDto teachManageStaffTeachRespDto : teachManageStaffTeachRespDtos) {
            if (teachManageStaffTeachRespDto.getStaffId().equals(staffId)) {
                for (TeachManageStaffTeachRespDto.TeachClassInfo teachClassInfo : teachManageStaffTeachRespDto.getTeachClassInfos()) {
                    if (teachClassInfo.getClassId().equals(saasClassId)) {
                        for (TeachManageStaffTeachRespDto.TeachClassInfo.Subject subject : teachClassInfo.getSubjects()) {
                            SubjectDto subjectDto = new SubjectDto();
                            subjectDto.setSubjectId(subject.getSubjectId());
                            subjectDto.setSubjectName(subject.getSubjectName());
                            subjectDtoList.add(subjectDto);
                        }

                    }
                }
            }

        }

        return subjectDtoList;
    }


    @Override
    public Map<Long, RoleEnum> getUserIdWithRoleCodeMap(Map<Long, Long> userIdWithStaffIdMap, String saasClassId, List<TeachManageStaffTeachRespDto> teachManageStaffTeachRespDtos) {

        if (CollectionUtils.isEmpty(userIdWithStaffIdMap) || CollectionUtils.isEmpty(teachManageStaffTeachRespDtos)) {
            return new HashMap<>();
        }

        Map<Long, RoleEnum> map = new HashMap<>();
        for (Map.Entry<Long, Long> longLongEntry : userIdWithStaffIdMap.entrySet()) {
            RoleEnum roleEnum = getRoleEnum(teachManageStaffTeachRespDtos, longLongEntry.getValue(), Long.valueOf(saasClassId));
            map.put(longLongEntry.getKey(), roleEnum);
        }

        return map;
    }

    private RoleEnum getRoleEnum(List<TeachManageStaffTeachRespDto> teachManageStaffTeachRespDtos, Long staffId, Long saasClassId) {

        if (!CollectionUtils.isEmpty(teachManageStaffTeachRespDtos)) {
            //班主任判断
            for (TeachManageStaffTeachRespDto teachManageStaffTeachRespDto : teachManageStaffTeachRespDtos) {
                if (teachManageStaffTeachRespDto.getStaffId().equals(staffId)) {
                    for (TeachManageStaffTeachRespDto.LeaderClassInfo leaderClassInfo : teachManageStaffTeachRespDto.getLeaderClassInfos()) {
                        if (leaderClassInfo.getClassId().equals(saasClassId)) {
                            return RoleEnum.HEAD_TEACHER;
                        }
                    }
                }
            }
            //任课老师判断
            for (TeachManageStaffTeachRespDto teachManageStaffTeachRespDto : teachManageStaffTeachRespDtos) {

                if (teachManageStaffTeachRespDto.getStaffId().equals(staffId)) {
                    for (TeachManageStaffTeachRespDto.TeachClassInfo teachClassInfo : teachManageStaffTeachRespDto.getTeachClassInfos()) {
                        if (teachClassInfo.getClassId().equals(saasClassId)) {
                            return RoleEnum.GENERAL_TEACHER;
                        }
                    }
                }
            }
        }

        return RoleEnum.NONE;

    }

}

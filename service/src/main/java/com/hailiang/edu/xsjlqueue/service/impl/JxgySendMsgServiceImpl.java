package com.hailiang.edu.xsjlqueue.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.hailiang.edu.xsjlqueue.component.sls.SlsComponent;
import com.hailiang.edu.xsjlqueue.component.sls.dto.ReportPointDto;
import com.hailiang.edu.xsjlqueue.consts.SmsLinkCodeConst;
import com.hailiang.edu.xsjlqueue.convert.ReportPointDtoConvert;
import com.hailiang.edu.xsjlqueue.convert.SendWxTemplateMsgReqConvert;
import com.hailiang.edu.xsjlqueue.dal.biz.SmsLinkSignStorage;
import com.hailiang.edu.xsjlqueue.dal.dao.XsUserMapper;
import com.hailiang.edu.xsjlqueue.dal.entity.XsSmsReport;
import com.hailiang.edu.xsjlqueue.dal.entity.XsUser;
import com.hailiang.edu.xsjlqueue.dto.jxgy.StudentDto;
import com.hailiang.edu.xsjlqueue.dto.jxgy.req.SendWxTemplateMsgReq;
import com.hailiang.edu.xsjlqueue.dto.jxgy.resp.FamilyStatusResp;
import com.hailiang.edu.xsjlqueue.dto.jxgy.resp.SendWxTemplateMsgResp;
import com.hailiang.edu.xsjlqueue.dto.saas.req.UnderClassInfoReqDto;
import com.hailiang.edu.xsjlqueue.dto.saas.resp.UnderClassInfoRespDto;
import com.hailiang.edu.xsjlqueue.enums.MiniProgramEnvEnum;
import com.hailiang.edu.xsjlqueue.query.UserQuery;
import com.hailiang.edu.xsjlqueue.remote.biz.jxgy.JxgyClient;
import com.hailiang.edu.xsjlqueue.remote.biz.saas.SaasClient;
import com.hailiang.edu.xsjlqueue.service.JxgySendMsgService;
import com.hailiang.edu.xsjlqueue.util.LogFormatUtil;
import com.hailiang.feign.CheetahMessageService;
import com.hailiang.helper.CheetahSmsHelper;
import com.hailiang.util.R;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class JxgySendMsgServiceImpl implements JxgySendMsgService {

    @Resource
    XsUserMapper xsUserMapper;
    @Resource
    SendWxTemplateMsgReqConvert sendWxTemplateMsgReqConvert;
    @Resource
    JxgyClient jxgyClient;
    @Resource
    LogFormatUtil logFormatUtil;
    @Resource
    SaasClient saasClient;
    @Resource
    CheetahMessageService cheetahMessageService;
    @Resource
    SmsLinkSignStorage smsLinkSignStorage;
    @Resource
    ReportPointDtoConvert reportPointDtoConvert;
    @Resource
    SlsComponent slsComponent;

    @Value("${cheetah.message.appId}")
    String cheetahMessageAppId;

    @Value("${cheetah.message.appSecret}")
    String cheetahMessageAppSecret;

    @Value("${cheetah.message.url}")
    String cheetahMessageUrl;

    @Value("${jxgy.h5.env}")
    String jxgyH5Env;

    @Value("${cheetah.message.msg.open}")
    Boolean msgOpen;


    private UnderClassInfoRespDto getClassInfo(String saasClassId) {
        //获取班级信息
        UnderClassInfoReqDto underClassInfoReqDto = new UnderClassInfoReqDto();
        List<Long> classIdList = new ArrayList<>();
        classIdList.add(Long.valueOf(saasClassId));
        underClassInfoReqDto.setClassIdList(classIdList);
        List<UnderClassInfoRespDto> underClassInfoRespDtoList = saasClient.getUnderClassInfoList(underClassInfoReqDto);
        UnderClassInfoRespDto underClassInfoRespDto = null;
        if (!CollectionUtils.isEmpty(underClassInfoRespDtoList)) {
            underClassInfoRespDto = underClassInfoRespDtoList.get(0);
        }

        return underClassInfoRespDto;
    }

    @Override
    public void sendWxMsg(List<StudentDto> waitMsgStudentList, List<StudentDto> waitWxStudentList, XsSmsReport xsSmsReport, Set<Long> userIds) {

        UserQuery userQuery = new UserQuery();
        userQuery.setUserIds(userIds);
        List<XsUser> xsUserList = xsUserMapper.getListByCondition(userQuery);
        Map<Long, XsUser> xsUserMap = xsUserList.stream().collect(Collectors.toMap(XsUser::getUserId, input -> input, (p1, p2) -> p2));

        //获取班级信息
        UnderClassInfoRespDto underClassInfoRespDto = getClassInfo(xsSmsReport.getSaasClassId());

        String className = sendWxTemplateMsgReqConvert.getClassname(underClassInfoRespDto);

        //发送家长短信模版
        if (!CollectionUtils.isEmpty(waitMsgStudentList) && msgOpen) {
            for (StudentDto studentDto : waitMsgStudentList) {

                //加密后的linkSign
                String linkSign = smsLinkSignStorage.setGetSmsLinkSign(xsSmsReport.getSmsReportId(), Long.valueOf(studentDto.getStudentId()));

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("studentName", studentDto.getStudentName());

                String rangeTime = sendWxTemplateMsgReqConvert.getRangeTime(xsSmsReport.getStartTime(), xsSmsReport.getEndTime());
                jsonObject.put("rangeTime", "（" + rangeTime + "）");

                String linkurl = "";
                String smsCode = SmsLinkCodeConst.CODE_ONE;

                //激活时 推送小程序打开h5链接
                if (studentDto.getFamilyStatus().equals(FamilyStatusResp.FAMILY_STATUS_YES)) {
                    //拼接短信链接
                    linkurl = "?linkSign=" + linkSign + "&type=report";

                    //拼接环境变量参数
                    if (!Objects.equals(jxgyH5Env, MiniProgramEnvEnum.RELEASE.getCode())) {
                        linkurl += "&env=" + jxgyH5Env;
                    }
                } else {
                    //未激活直接推送h5链接
                    linkurl = "h5/report/detail?linkSign=" + linkSign;
                    smsCode = SmsLinkCodeConst.CODE_THREE;
                }

                jsonObject.put("linkUrl", linkurl);
                StringBuilder mobile = new StringBuilder();
                if (CollUtil.isEmpty(studentDto.getFamilyMobiles())) {
                    logFormatUtil.formatInfo(studentDto.getStudentName() + "无家长数据不推送短信:" + JSONObject.toJSONString(studentDto.getFamilyMobiles()));
                    continue;
                }
                for (String number : studentDto.getFamilyMobiles()) {
                    mobile.append(number).append(",");
                }

                mobile = new StringBuilder(StrUtil.removeSuffix(mobile.toString(), ","));
                R r = cheetahMessageService.send(CheetahSmsHelper.buildParam("xsjlsys", cheetahMessageAppId, smsCode, jsonObject.toString(), mobile.toString()));
                logFormatUtil.formatInfo("报告id:" + xsSmsReport.getSmsReportId() + studentDto.getStudentName() + "家长的手机号:" + JSONObject.toJSONString(mobile));
                logFormatUtil.formatInfo("报告id:" + xsSmsReport.getSmsReportId() + studentDto.getStudentName() + "家长短信发送结果:" + JSONObject.toJSONString(r));
                logFormatUtil.formatInfo("报告id:" + xsSmsReport.getSmsReportId() + studentDto.getStudentName() + "短信链接code:" + smsCode + "短信url" + linkurl);
            }
        }


        //发送微信公众号信息
        if (!CollectionUtils.isEmpty(waitWxStudentList)) {
            UserQuery userQuery1 = new UserQuery();
            userQuery1.setUserId(xsSmsReport.getUserId());
            XsUser xsUser = xsUserMapper.getRowByCondition(userQuery1);
            SendWxTemplateMsgReq sendWxTemplateMsgReq = sendWxTemplateMsgReqConvert.toObj(waitWxStudentList, xsSmsReport, xsUser, className);
            List<SendWxTemplateMsgResp> sendWxTemplateMsgRespList = jxgyClient.sendWxTemplateMsg(sendWxTemplateMsgReq);
            logFormatUtil.formatInfo("报告id:" + xsSmsReport.getSmsReportId() + "sendWxTemplateMsgReq:" + JSONObject.toJSONString(sendWxTemplateMsgReq));
            logFormatUtil.formatInfo("报告id:" + xsSmsReport.getSmsReportId() + "家长模版发送结果:" + JSONObject.toJSONString(sendWxTemplateMsgRespList));
        }

        //给教师发短信
        if (!CollectionUtils.isEmpty(xsUserMap) && msgOpen) {

            for (Map.Entry<Long, XsUser> entry : xsUserMap.entrySet()) {

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("teacherName", entry.getValue().getAccountName());
                jsonObject.put("className", className);
                String rangeTime = sendWxTemplateMsgReqConvert.getRangeTime(xsSmsReport.getStartTime(), xsSmsReport.getEndTime());
                jsonObject.put("rangeTime", rangeTime);

                StringBuilder mobile = new StringBuilder();
                mobile.append(entry.getValue().getPhone()).append(",");

                mobile = new StringBuilder(StrUtil.removeSuffix(mobile.toString(), ","));
                String smsCode = SmsLinkCodeConst.CODE_TWO;
                R r = cheetahMessageService.send(CheetahSmsHelper.buildParam("xsjlsys", cheetahMessageAppId, smsCode, jsonObject.toString(), mobile.toString()));
                logFormatUtil.formatInfo("报告id:" + xsSmsReport.getSmsReportId() + "教师mobile:" + JSONObject.toJSONString(mobile));
                logFormatUtil.formatInfo("报告id:" + xsSmsReport.getSmsReportId() + "教师短信发送结果:" + JSONObject.toJSONString(r));
            }
        }

        doSlsPoint(waitMsgStudentList, waitWxStudentList, xsSmsReport, underClassInfoRespDto);
    }

    //数据埋点
    private void doSlsPoint(List<StudentDto> waitMsgStudentList, List<StudentDto> waitWxStudentList
            , XsSmsReport xsSmsReport, UnderClassInfoRespDto underClassInfoRespDto) {

        try {
            List<ReportPointDto> reportPointDtoList = new ArrayList<>();
            reportPointDtoList.addAll(reportPointDtoConvert.toList(waitMsgStudentList, xsSmsReport, underClassInfoRespDto, "sms"));
            reportPointDtoList.addAll(reportPointDtoConvert.toList(waitWxStudentList, xsSmsReport, underClassInfoRespDto, "wx"));

            slsComponent.pushLog(reportPointDtoList);
        } catch (Exception e) {
            logFormatUtil.exceptionPrint(e);
        }

    }


}

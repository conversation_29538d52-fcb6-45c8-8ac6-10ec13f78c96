package com.hailiang.edu.xsjlqueue.component;


import cn.hutool.core.date.DateUtil;
import com.hailiang.edu.xsjlqueue.query.PointRecordQuery;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class TimeComponent {

    /**
     * 设置 周为周期 上周几到昨天 时间段
     *
     * @param pointRecordQuery
     * @param date
     */
    public void setWeekTime(PointRecordQuery pointRecordQuery, Date date) {
        //上周
        Date lastWeek = DateUtil.offsetWeek(date, -1);
        //昨天
        Date yesterDay = DateUtil.offsetDay(date, -1);
        String startTime = DateUtil.beginOfDay(lastWeek).toString();
        //获取结束时间
        String endTime = DateUtil.endOfDay(yesterDay).toString();
        pointRecordQuery.setStartTime(startTime);
        pointRecordQuery.setEndTime(endTime);
    }

    public void setMonthTime(PointRecordQuery pointRecordQuery, Date date) {
        //上月
        Date lastMonth = DateUtil.offsetMonth(date, -1);
        //昨天
        Date yesterDay = DateUtil.offsetDay(date, -1);
        String startTime = DateUtil.beginOfDay(lastMonth).toString();
        //获取结束时间
        String endTime = DateUtil.endOfDay(yesterDay).toString();

        pointRecordQuery.setStartTime(startTime);
        pointRecordQuery.setEndTime(endTime);
    }

    public void setDefineTime(PointRecordQuery pointRecordQuery, Date date, int intervalDay) {
        //上次间隔天数
        Date last = DateUtil.offsetDay(date, -intervalDay);
        //昨天
        Date yesterDay = DateUtil.offsetDay(date, -1);
        String startTime = DateUtil.beginOfDay(last).toString();
        //获取结束时间
        String endTime = DateUtil.endOfDay(yesterDay).toString();

        pointRecordQuery.setStartTime(startTime);
        pointRecordQuery.setEndTime(endTime);

    }

}

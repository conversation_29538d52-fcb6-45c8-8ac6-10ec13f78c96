/**
 * Hailiang.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.hailiang.edu.xsjlqueue.service;

import java.util.concurrent.ExecutionException;

import com.hailiang.edu.xsjlqueue.dto.stu.StudentDataSyncTaskDto;

/**
 * 星动力学生与saas学生比对接口定义类
 * <AUTHOR>
 * @version v0.1: StudentDataSyncService.java, v 0.1 2023年09月19日 19:31  zhousx Exp $
 */
public interface StudentDataSyncService {

    void doStuSync(StudentDataSyncTaskDto taskDto) throws ExecutionException, InterruptedException;

}
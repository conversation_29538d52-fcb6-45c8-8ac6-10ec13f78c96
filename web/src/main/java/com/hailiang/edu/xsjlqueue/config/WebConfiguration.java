package com.hailiang.edu.xsjlqueue.config;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.hailiang.edu.xsjlqueue.handler.InitHandlerInterceptor;
import com.hailiang.edu.xsjlqueue.handler.LoginHandlerInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

/**
 * 基本配置
 *
 */
@Configuration
public class WebConfiguration extends WebMvcConfigurationSupport {


    @Autowired
    private InitHandlerInterceptor initHandlerInterceptor;

    @Autowired
    private LoginHandlerInterceptor loginHandlerInterceptor;

    /**
     * 拦截器注入进来
     *
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //初始拦截器
        registry.addInterceptor(initHandlerInterceptor).addPathPatterns("/**");
        // 拦截所有请求，通过判断是否有 @LoginRequired 注解 决定是否需要登录
        registry.addInterceptor(loginHandlerInterceptor).addPathPatterns("/**");
    }

    /**
     * 响应结果序列化使用fastjson进行处理
     *
     */
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        FastJsonHttpMessageConverter converter = new FastJsonHttpMessageConverter();
        FastJsonConfig config = new FastJsonConfig();
        config.setSerializerFeatures(
                // 保留map空的字段
//                SerializerFeature.WriteMapNullValue,
                // 将String类型的null转成""
//                SerializerFeature.WriteNullStringAsEmpty,
                // 将Number类型的null转成0
//                SerializerFeature.WriteNullNumberAsZero,
                // 将List类型的null转成[]
//                SerializerFeature.WriteNullListAsEmpty,
                // 将Boolean类型的null转成false
//                SerializerFeature.WriteNullBooleanAsFalse,
                // 避免循环引用
                SerializerFeature.DisableCircularReferenceDetect);

        converter.setFastJsonConfig(config);
        converter.setDefaultCharset(Charset.forName("UTF-8"));
        List<MediaType> mediaTypeList = new ArrayList<>();
        // 解决中文乱码问题，相当于在Controller上的@RequestMapping中加了个属性produces = "application/json"
        mediaTypeList.add(MediaType.APPLICATION_JSON);
        converter.setSupportedMediaTypes(mediaTypeList);
        converters.add(converter);
    }

}
package com.hailiang.edu.xsjlqueue;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;


@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.hailiang.*")
@EnableScheduling
@ImportResource({"classpath*:META-INF/spring/*.xml" })
@EnableAsync
public class xsjlqueueApplication {

    public static void main(String[] args) {
        SpringApplication.run(xsjlqueueApplication.class, args);
    }

    @Bean({ "threadPoolTaskExecutor" })
    public ThreadPoolTaskExecutor threadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(8);
        executor.setMaxPoolSize(32);
        executor.setQueueCapacity(2048);
        return executor;
    }

}

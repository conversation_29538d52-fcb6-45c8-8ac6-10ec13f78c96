package com.hailiang.edu.xsjlqueue.handler;

import com.hailiang.edu.xsjlqueue.enums.ApiCodeEnum;
import com.hailiang.edu.xsjlqueue.exception.BusinessException;
import com.hailiang.edu.xsjlqueue.dto.ResultJson;

import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * 全局异常拦截捕捉
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandlerInterceptor {

    @ExceptionHandler(value=Exception.class) //该注解声明异常处理方法
    public ResultJson exceptionHandler(HttpServletRequest request, Exception e){
//        e.printStackTrace();
        if(e instanceof BusinessException){
            return new ResultJson(((BusinessException) e).getCode(),e.getMessage());
        }


        return new ResultJson(ApiCodeEnum.NORMAL_ERROR.getCode(),e.getMessage());
    }
}
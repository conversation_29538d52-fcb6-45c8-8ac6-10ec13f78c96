package com.hailiang.edu.xsjlqueue.handler;


import com.hailiang.edu.xsjlqueue.annotation.LoginRequired;
import com.hailiang.edu.xsjlqueue.component.jwt.JwtToken;
import com.hailiang.edu.xsjlqueue.dto.UserInfo;
import com.hailiang.edu.xsjlqueue.enums.ApiCodeEnum;
import com.hailiang.edu.xsjlqueue.exception.BusinessException;
import lombok.extern.log4j.Log4j2;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

/**
 * 判断接口是否需要登录拦截器
 *
 * <AUTHOR>
 */
@Component
@Log4j2
public class LoginHandlerInterceptor extends HandlerInterceptorAdapter {


    @Resource
    JwtToken jwtToken;

    /**
     * 在请求处理之前进行调用（Controller方法调用之前）
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 如果不是映射到方法直接通过
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        // ①:START 方法注解级拦截器
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        // 判断接口是否需要登录
        LoginRequired methodAnnotation = method.getAnnotation(LoginRequired.class);
        String path = request.getRequestURI();
        log.info("请求的path  {}", path);

        // 有 @LoginRequired 注解，需要认证
        if (methodAnnotation != null) {
            //  拦截的具体规则
            String token = request.getHeader("token");
            log.info("token:{}", token);
            if (null == token) {
                throw new BusinessException("未登录", ApiCodeEnum.LOGIN_ERROR.getCode());
            }
            UserInfo userInfo = jwtToken.decodeUserInfo(token);
            if (null == userInfo) {
                throw new BusinessException("登录异常", ApiCodeEnum.LOGIN_ERROR.getCode());
            }

            request.setAttribute("userInfo", userInfo);


            return true;
        }
        return true;

    }

    /**
     * 请求处理之后进行调用，但是在视图被渲染之前（Controller方法调用之后）
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, @Nullable ModelAndView modelAndView) throws Exception {
    }

    /**
     * 在整个请求结束之后被调用，也就是在DispatcherServlet 渲染了对应的视图之后执行（主要是用于进行资源清理工作）
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, @Nullable Exception ex) throws Exception {
    }

}
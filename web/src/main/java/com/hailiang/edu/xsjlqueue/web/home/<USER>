package com.hailiang.edu.xsjlqueue.web.home;

import com.hailiang.edu.xsjlqueue.component.nacos.NacosComponent;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@Controller
public class IndexController {

    @Resource
    NacosComponent nacosComponent;

    @RequestMapping("/")
    public void index(HttpServletResponse response) throws Exception {
        response.getWriter()
            .println("<html><head><meta charset=\"utf-8\"><meta name=\"viewport\" "
                     + "content=\"width=device-width, initial-scale=1.0\"><title>Index"
                     + "</title></head><body><h1>Hello World!</h1></body></html>");
    }


    @RequestMapping("/nacos_down")
    public void nacosDown(HttpServletResponse response) throws Exception {
        String res = nacosComponent.doCancelInstance();
        response.getWriter()
                .println("<html><head><meta charset=\"utf-8\"><meta name=\"viewport\" "
                        + "content=\"width=device-width, initial-scale=1.0\"><title>Index"
                        + "</title></head><body><h1> " + res + "</h1></body></html>");
    }

}

package com.hailiang.edu.xsjlqueue.web.api.v1;

import com.hailiang.edu.xsjlqueue.api.v1.DemoApi;
import com.hailiang.edu.xsjlqueue.consts.ApiCodeConst;
import com.hailiang.edu.xsjlqueue.dto.ResultJson;
import com.hailiang.edu.xsjlqueue.reqo.DemoReq;
import com.hailiang.edu.xsjlqueue.business.DemoBusiness;
import com.hailiang.edu.xsjlqueue.validate.CommonValidate;
import com.hailiang.edu.xsjlqueue.annotation.LoginRequired;
import com.hailiang.edu.xsjlqueue.dto.UserInfo;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestAttribute;

import javax.annotation.Resource;

@RestController("V1Demo")
@SuppressWarnings("all")
public class DemoController implements DemoApi {

    @Resource
    CommonValidate commonValidate;

    @Resource
    DemoBusiness demoBusiness;

    /**
     * get请求demo
     * @return
     */
    @Override
    public ResultJson index() {
        return demoBusiness.index();
    }

    /**
     * post json方式请求 验证传参 demo
     * @param demoReq
     * @return
     */
    @Override
    public ResultJson validate(@RequestBody DemoReq demoReq) {

        ResultJson resultJson = commonValidate.execute(demoReq, "validate");
        if (ApiCodeConst.CODE_SUCCESS != resultJson.getCode()) {
            return resultJson;
        }

        return demoBusiness.validate(demoReq);
    }

    /**
     * post json 方式请求，验证传参以及需要登录才能请求 demo
     * @param demoReq
     * @param userInfo
     * @return
     */
    @Override
    @LoginRequired
    public ResultJson validateLogin(@RequestBody DemoReq demoReq,@RequestAttribute("userInfo") UserInfo userInfo) {

        ResultJson resultJson = commonValidate.execute(demoReq, "validateLogin");
        if (ApiCodeConst.CODE_SUCCESS != resultJson.getCode()) {
            return resultJson;
        }

        return demoBusiness.validateLogin(demoReq,userInfo);
    }

}
logging.file.path=./logs
logging.level.root=info
server.servlet.context-path=/queue
spring.application.name=XSJLQUEUE
server.port=12002

# Nacos ä½ä¸ºéç½®ä¸­å¿
spring.cloud.nacos.config.enabled=true
### Nacos ä½ä¸ºæå¡ä¸­å¿
spring.cloud.nacos.discovery.enabled=true
###éç½®æä»¶éæ©
spring.profiles.active={env}

mybatis.mapper-locations=classpath:mapper/*.xml

#æå°sqlæ¥å¿
#logging.level.com.hailiang.edu.xsjlqueue.dal.dao=debug

#å¬å±ç»ä¸éç½®
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver


feign.client.config.default.connect-timeout=15000
feign.client.config.default.read-timeout=60000

#æ°æ®åºè¿æ¥æ± éç½®
#åå§åè¿æ¥æ± çè¿æ¥æ°é å¤§å°ï¼æå°ï¼æå¤§
spring.datasource.druid.initial-size=8
spring.datasource.druid.min-idle=5
spring.datasource.druid.max-active=20
#éç½®è·åè¿æ¥ç­å¾è¶æ¶çæ¶é´
spring.datasource.druid.max-wait=60000
#éç½®é´éå¤ä¹æè¿è¡ä¸æ¬¡æ£æµï¼æ£æµéè¦å³é­çç©ºé²è¿æ¥ï¼åä½æ¯æ¯«ç§
spring.datasource.druid.time-between-eviction-runs-millis=60000
# éç½®ä¸ä¸ªè¿æ¥å¨æ± ä¸­æå°çå­çæ¶é´ï¼åä½æ¯æ¯«ç§
spring.datasource.druid.min-evictable-idle-time-millis=30000
spring.datasource.druid.validation-query=SELECT 1 FROM DUAL
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=true
spring.datasource.druid.test-on-return=false

spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0
spring.redis.timeout=10000


server.max-http-header-size=10MB

spring.main.allow-bean-definition-overriding=true



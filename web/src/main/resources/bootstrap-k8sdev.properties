#Nacos æå¡å¨å°å
spring.cloud.nacos.config.server-addr=10.30.5.104:8848
# ä½¿ç¨ç Nacos çå½åç©ºé´ï¼é»è®¤ä¸º null
spring.cloud.nacos.config.namespace=26e2f2fe-c9a6-4fe8-8081-8fa132e80073
# ä½¿ç¨ç Nacos éç½®åç»ï¼é»è®¤ä¸º DEFAULT_GROUP
spring.cloud.nacos.config.group=DEFAULT_GROUP
# ä½¿ç¨ç Nacos éç½®éç dataIdï¼é»è®¤ä¸º spring.application.name
spring.cloud.nacos.config.name=${spring.application.name}
# ä½¿ç¨ç Nacos éç½®éç dataId çæä»¶æå±åï¼åæ¶ä¹æ¯ Nacos éç½®éçéç½®æ ¼å¼ï¼é»è®¤ä¸º properties
spring.cloud.nacos.config.file-extension=properties
# Nacos ä½ä¸ºæå¡ä¸­å¿
spring.cloud.nacos.discovery.server-addr=10.30.5.104:8848
spring.cloud.nacos.discovery.service=${spring.application.name}
spring.cloud.nacos.discovery.namespace=26e2f2fe-c9a6-4fe8-8081-8fa132e80073
# 1 second, see the ISO 8601 standard
spring.messages.cache-duration=PT1S

###æå¡æ³¨å
spring.cloud.nacos.discovery.username=yanfa2
spring.cloud.nacos.discovery.password=zykSts231
####æå¡éç½®
spring.cloud.nacos.config.username=yanfa2
spring.cloud.nacos.config.password=zykSts231
package com.hailiang.edu.xsjlqueue.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.log.exception.LogException;
import com.hailiang.edu.xsjlqueue.component.sls.SlsComponent;
import com.hailiang.edu.xsjlqueue.dto.saas.resp.SaasSchoolRespDto;
import com.hailiang.edu.xsjlqueue.dto.stu.StudentDataSyncTaskDto;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.concurrent.ExecutionException;

@SpringBootTest
public class LogTest {

    @Resource
    SlsComponent slsComponent;

    @Resource
    private StudentDataSyncService studentDataSyncService;

    @Resource
    private SaasService saasService;

    @Test
    public void index() throws ExecutionException, InterruptedException {

//        slsComponent.pushLog();
        String json = "{\"stuSyncTaskId\":902728445399007232,\"schoolIds\":[6999767865909256192,6941817926003531776,6941818695494737920,6941818255675826176,6941818585356509184],\"msgCount\":15,\"syncJobId\":902728445378035712}";

        StudentDataSyncTaskDto taskDto = JSONObject.parseObject(json,StudentDataSyncTaskDto.class);

        studentDataSyncService.doStuSync(taskDto);


    }

    @Test
    public void test(){
        Long schoolId = 7092829793375809536L;
        SaasSchoolRespDto saasSchoolRespDto = saasService.getSchoolInfo(schoolId);

        System.out.println("12121");
    }

}

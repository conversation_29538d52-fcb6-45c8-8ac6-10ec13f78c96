package com.hailiang.edu.xsjlqueue.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hailiang.edu.xsjlqueue.dto.jxgy.req.FamilyStatusReq;
import com.hailiang.edu.xsjlqueue.dto.jxgy.resp.FamilyStatusResp;
import com.hailiang.edu.xsjlqueue.dto.saas.req.UnderClassInfoReqDto;
import com.hailiang.edu.xsjlqueue.dto.saas.resp.UnderClassInfoRespDto;
import com.hailiang.edu.xsjlqueue.dto.sms.SmsRuleTaskDto;
import com.hailiang.edu.xsjlqueue.remote.biz.jxgy.JxgyClient;
import com.hailiang.edu.xsjlqueue.remote.biz.saas.SaasClient;
import com.hailiang.edu.xsjlqueue.remote.biz.saas.feign.SaasFeignInner;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
@ActiveProfiles("k8stest")
public class ReportTest {

    @Resource
    SmsReportService smsReportService;

    @Resource
    SaasFeignInner saasFeignInner;

    @Resource
    SaasClient saasClient;

    @Resource
    JxgyClient jxgyClient;

    @Test
    public void jxgyTest() {

//        FamilyStatusReq familyStatusReq = new FamilyStatusReq();
//
//
//        List<FamilyStatusReq.StudentDto> studentDtos = new ArrayList<>();
//        FamilyStatusReq.StudentDto studentDto = new FamilyStatusReq.StudentDto();
//        studentDto.setStudentCode("11222222");
//        studentDto.setStudentName("");
//
//        familyStatusReq.setStudentList(studentDtos);


        String json = "[{\"studentCode\":\"11222222\",\"studentName\":\"边一杰\"},{\"studentCode\":\"17308\",\"studentName\":\"寿董娜\"}]";
        List<FamilyStatusReq.StudentDto> studentDtos = JSONArray.parseArray(json, FamilyStatusReq.StudentDto.class);

        FamilyStatusReq familyStatusReq = new FamilyStatusReq();
        familyStatusReq.setStudentList(studentDtos);
        List<FamilyStatusResp> familyStatusRespList = jxgyClient.getFamilyStatusList(familyStatusReq);

        System.out.println(JSONObject.toJSONString(familyStatusRespList));

/*        String json2 = "{\n" +
                "    \"appid\": \"S43htV1AaAYavMgQ\",\n" +
                "    \"first\": \"您有一份新的学生积分报告待查收\",\n" +
                "    \"keyword1\": \"小班1班\",\n" +
                "    \"keyword2\": \"李永康-开发\",\n" +
                "    \"keyword3\": \"2023-03-29 13:48:58\",\n" +
                "    \"remark\": \"请及时点击查看\",\n" +
                "    \"nonceStr\": \"ibuaiVcKdpRxkhJA\",\n" +
                "    \"sign\": \"DCEA372D7010A0A14030E3FAB0AA905E\",\n" +
                "    \"timestamp\": 1673934538,\n" +
                "    \"type\": \"internalDrive\",\n" +
                "    \"url\": \"http://10.30.5.34:20130/quality-evaluate-h5/home/<USER>",\n" +
                "    \"urlType\": 0,\n" +
                "    \"studentList\": [\n" +
                "        {\n" +
                "            \"studentCode\": \"20190109639\",\n" +
                "            \"studentName\": \"林十一\",\n" +
                "            \"keyword4\": \"张文文2023-1-12至2023-2-11的积分表现报告已生成\",\n" +
                "            \"urlParam\": \"id=10\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";

        SendTemplateMsgReq sendTemplateMsgReq = JSONObject.parseObject(json2,SendTemplateMsgReq.class);


        jxgyClient.sendTemplateMsg(sendTemplateMsgReq);*/


        System.out.println("12121");
    }

    @Test
    public void index() {

/*        StaffUnderTenantReqDto staffUnderTenantReqDto = new StaffUnderTenantReqDto();
        staffUnderTenantReqDto.setTenantId(6861533837994209280L);

        List<Long> userIdList = new ArrayList<>();
        userIdList.add(6854747466805723136L);
        staffUnderTenantReqDto.setUserIdList(userIdList);
        CommonResultRespDto<List<StaffUnderTenantRespDto>> commonResultRespDto =  saasFeignInner.getSaasStaffListUnderTenant(staffUnderTenantReqDto);

        System.out.println("1212121");*/
        SmsRuleTaskDto smsRuleTaskDto = new SmsRuleTaskDto();
        smsRuleTaskDto.setSmsRuleId(215L);


//        SmsRuleTaskDto sms = JSON.parseObject("{\"smsRuleTaskId\":932728402949832704,\"smsRuleId\":108,\"createTime\":\"2023-12-14 17:30:46\",\"updateTime\":\"2023-12-14 17:30:46\"}"
//                , SmsRuleTaskDto.class);

        smsReportService.doGenSmsReport(smsRuleTaskDto);
    }

    @Test
    public void tt() {

        UnderClassInfoReqDto underClassInfoReqDto = new UnderClassInfoReqDto();
        List<Long> classIdList = new ArrayList<>();
        classIdList.add(6884781747586105369L);
        underClassInfoReqDto.setClassIdList(classIdList);

        List<UnderClassInfoRespDto> underClassInfoRespDtoList = saasClient.getUnderClassInfoList(underClassInfoReqDto);


        System.out.println("1212121");
    }


}

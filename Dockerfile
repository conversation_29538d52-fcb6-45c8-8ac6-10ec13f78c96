# 指定基础镜像
FROM swr.cn-east-3.myhuaweicloud.com/hlyz/jdk8-dragonwell:8u382
# 维护者信息
LABEL maintainer="<EMAIL>"
# 定义变量
ARG DST_DIR=/opt/app
ARG java_options="{{JVM_OPTIONS}}"
ARG app_name={{APP_NAME}}
ENV JAVA_OPTIONS=${java_options}
ENV APP_NAME=${app_name}
# 创建应用目录
RUN mkdir -p ${DST_DIR}
# 添加普通用户
RUN groupadd -g 2000 hljy && useradd -u 1000 -g 2000  hljy
# 进入工作目录
WORKDIR ${DST_DIR}
# 复制jar
COPY web/target/*.jar	./
COPY web/target/classes/*.properties ./
COPY web/target/classes/*.xml  ./
COPY *.sh ./

# /opt/app目录给hljy授权
RUN chown -R hljy.hljy ${DST_DIR} \
&& rm -f /etc/localtime \
#同步docker时区
&& ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
&& echo "Asia/Shanghai" > /etc/timezone

# 切换用户
USER hljy
# EXPOSE 映射端口
EXPOSE 12002
# ENTRYPOINT 处理pid=1的问题
#ENTRYPOINT ["/sbin/tini", "--"]
# CMD 运行以下命令(如果yaml文件定义了command会被覆盖)
ENTRYPOINT ["/bin/sh","-c","set -e && java ${JAVA_OPTIONS} -jar  ${APP_NAME}.jar "]
#!/bin/bash
source /etc/profile
echo "---start xsjlqueue ---"



dest=bootstrap.properties

dynamic_update=1
if [ ${dynamic_update} -eq 1 ]
then
   sed -i -e "s#{env}#$env#g" ${dest}
else
   echo "needn't update config, start directly..."
fi


chmod +x xsjlqueue.jar
ulimit -c unlimited
ulimit -n 51200
nohup java -server -Ddruid.mysql.usePingMethod=false -Dio.netty.leakDetectionLevel=advanced -Djava.security.egd=file:/dev/./urandom -Xms512m -Xmx512m -Xss512K -XX:+HeapDumpOnOutOfMemoryError -jar xsjlqueue.jar>/dev/null 2>&1&

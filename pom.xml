<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.hailiang.edu</groupId>
    <artifactId>xsjlqueue-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>hailiang-xsjlqueue-parent</name>
    <description>海亮xsjlqueue工程</description>
    <packaging>pom</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.2.RELEASE</version>
    </parent>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring.cloud.alibaba.version>2.2.3.RELEASE</spring.cloud.alibaba.version>
        <spring.cloud.version>2.2.5.RELEASE</spring.cloud.version>
        <mapstruct.version>1.3.1.Final</mapstruct.version>
        <lombok.version>1.18.6</lombok.version>
        <openfeign.version>2.2.5.RELEASE</openfeign.version>
        <knife4j.version>2.0.8</knife4j.version>
        <common-api.version>1.0.0-SNAPSHOT</common-api.version>
        <common-core.version>1.0.0-SNAPSHOT</common-core.version>
        <api.version>1.0.0-SNAPSHOT</api.version>
    </properties>

    <modules>
        <module>api</module>
        <module>dal</module>
        <module>remote</module>
        <module>service</module>
        <module>web</module>
    </modules>



    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${openfeign.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>6.5</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>nexus Repository RELEASES</name>
            <url>http://nexus.ehailiang.com:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>nexus Repository SNAPSHOTS</name>
            <url>http://nexus.ehailiang.com:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>  
        <pluginManagement>  
            <plugins>  
                <plugin>  
                    <groupId>org.apache.maven.plugins</groupId>  
                    <artifactId>maven-archetype-plugin</artifactId>  
                    <version>3.0.1</version>  
                </plugin>  
                <plugin>  
                    <groupId>org.apache.maven.plugins</groupId>  
                    <artifactId>maven-compiler-plugin</artifactId>  
                    <version>3.6.1</version>
                    <configuration>  
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok-maven-plugin</artifactId>
                                <version>1.16.20.0</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>  
                <plugin>  
                    <groupId>org.apache.maven.plugins</groupId>  
                    <artifactId>maven-resources-plugin</artifactId>  
                    <version>3.0.2</version>
                    <configuration>  
                        <encoding>UTF-8</encoding>  
                    </configuration>  
                </plugin>  
                <plugin>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <configuration>
                        <includes>
                            <include>**/*Test*.java</include>
                        </includes>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-source-plugin</artifactId>
                    <executions>
                        <execution>
                            <phase>compile</phase>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <configuration>
                        <mainClass>com.hailiang.edu.xsjlqueue.xsjlqueueApplication</mainClass>
                    </configuration>
                </plugin>
            </plugins>  
        </pluginManagement>  
    </build>

</project>
